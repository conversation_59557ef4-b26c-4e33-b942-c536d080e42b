name: 🚀 Deploy to Server

on:
  workflow_run:
    workflows: ["🧪 CI - Tests et Qualité"]
    types:
      - completed
    branches: [ main ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy'
        required: true
        default: 'production'
        type: choice
        options:
          - production

permissions:
  contents: read
  actions: write

env:
  PHP_VERSION: '8.3'

jobs:
  # Job 1: Deploy to Staging Server
  deploy-production:
    name: 🚀 Deploy Production
    runs-on: ubuntu-latest
    if: |
      (github.event_name == 'workflow_run' && github.event.workflow_run.conclusion == 'success') ||
      (github.event.inputs.environment == 'production')
    environment:
      name: production
      url: https://ecotask-staging.sami-rochdi.fr

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🚀 Deploy to Production Server (Docker)
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          port: ${{ secrets.SERVER_PORT || 22 }}
          script: |
            echo "🚀 Déploiement production Docker sur serveur..."

            # Aller dans le répertoire du projet
            cd ${{ secrets.SERVER_PATH }}/EcoTask || exit 1

            # Mettre à jour le code
            git fetch origin
            git reset --hard origin/main

            # Copier le fichier de configuration
            cp .env.production .env

            # Rendre le script exécutable
            chmod +x scripts/deploy-docker.sh

            # Exécuter le déploiement Docker
            ./scripts/deploy-docker.sh production

            echo "✅ Déploiement production Docker terminé"

      - name: 🔍 Health Check Production
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          port: ${{ secrets.SERVER_PORT || 22 }}
          script: |
            echo "🔍 Vérification de santé de la production sur le serveur..."

            # Test direct dans le conteneur
            if docker exec ecotask-app curl -f -s http://localhost > /dev/null 2>&1; then
              echo "✅ Application EcoTask opérationnelle"
              echo "📊 Statut des conteneurs:"
              docker ps --format "table {{.Names}}\t{{.Status}}" | grep ecotask
            else
              echo "❌ Application EcoTask non accessible"
              echo "📊 Logs de l'application:"
              docker logs ecotask-app --tail=10
              exit 1
            fi

      - name: 📢 Notification Production
        if: always()
        run: |
          STATUS="${{ job.status }}"
          if [ "$STATUS" = "success" ]; then
            MESSAGE="🎉 *Déploiement Production Réussi*%0A%0A"
          else
            MESSAGE="🚨 *Déploiement Production Échoué*%0A%0A"
          fi

          MESSAGE="${MESSAGE}🌐 *URL*: https://ecotask.sami-rochdi.fr%0A"
          MESSAGE="${MESSAGE}🔧 *Branche*: ${{ github.ref_name }}%0A"
          MESSAGE="${MESSAGE}📝 *Commit*: ${{ github.sha }}%0A"
          MESSAGE="${MESSAGE}⏰ *Timestamp*: $(date -u)%0A"
          MESSAGE="${MESSAGE}🔗 *Workflow*: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"

          curl -s -X POST "https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage" \
            -d "chat_id=${{ secrets.TELEGRAM_CHAT_ID }}" \
            -d "text=${MESSAGE}" \
            -d "parse_mode=Markdown" \
            -d "disable_web_page_preview=true" || echo "Notification Telegram échouée"



  # Job 2: Post-deployment Summary
  post-deployment:
    name: 📊 Post-deployment Summary
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: always() && needs.deploy-production.result == 'success'

    steps:
      - name: 📊 Deployment Summary
        run: |
          echo "📊 Résumé du déploiement"
          echo "======================="

          if [ "${{ needs.deploy-production.result }}" = "success" ]; then
            echo "✅ Production: Déployé avec succès"
            echo "🌐 URL: https://ecotask.sami-rochdi.fr"
            echo "📝 Commit: ${{ github.sha }}"
            echo "⏰ Timestamp: $(date -u)"
          else
            echo "❌ Production: Échec du déploiement"
          fi

          echo ""
          echo "💡 Le monitoring s'exécutera automatiquement selon son planning."
          echo "   Pour un monitoring immédiat, allez dans Actions > Monitoring > Run workflow"
