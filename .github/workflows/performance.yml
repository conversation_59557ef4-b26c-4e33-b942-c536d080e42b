name: ⚡ Tests de Performance

on:
  schedule:
    # Tous les jours à 2h00 UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      target_url:
        description: 'URL cible pour les tests'
        required: false
        default: 'http://localhost:8080'
        type: string
      test_duration:
        description: 'Durée des tests (secondes)'
        required: false
        default: '60'
        type: string
      concurrent_users:
        description: 'Nombre d utilisateurs simultanes'
        required: false
        default: '10'
        type: string
  workflow_run:
    workflows: ["🚀 Deploy to Server"]
    types:
      - completed
    branches: [ main ]

env:
  NODE_VERSION: '18'
  TARGET_URL: ${{ github.event.inputs.target_url || 'http://localhost:8080' }}
  TEST_DURATION: ${{ github.event.inputs.test_duration || '60' }}
  CONCURRENT_USERS: ${{ github.event.inputs.concurrent_users || '10' }}

jobs:
  # Job 1: Tests de performance avec Artillery
  artillery-tests:
    name: 🎯 Tests Artillery
    runs-on: ubuntu-latest
    if: |
      github.event_name == 'workflow_dispatch' ||
      github.event_name == 'schedule' ||
      (github.event_name == 'workflow_run' && github.event.workflow_run.conclusion == 'success')

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root_password
          MYSQL_DATABASE: ecotask_test
          MYSQL_USER: ecotask
          MYSQL_PASSWORD: ecotask_password
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🐘 Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          extensions: mbstring, xml, ctype, iconv, intl, pdo_mysql, redis
          tools: composer:v2

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install PHP dependencies
        run: composer install --prefer-dist --no-progress --no-suggest --no-scripts

      - name: 📥 Install Artillery
        run: |
          npm install -g artillery@latest
          artillery --version

      - name: 📝 Setup test environment
        run: |
          cp .env.example .env.test
          echo "APP_ENV=test" >> .env.test
          echo "APP_DEBUG=false" >> .env.test
          echo "APP_SECRET=test_secret_key" >> .env.test
          echo "DATABASE_URL=mysql://root:root_password@127.0.0.1:3306/ecotask_test" >> .env.test
          echo "REDIS_URL=redis://127.0.0.1:6379" >> .env.test

      - name: 🗄️ Setup Database
        env:
          DATABASE_URL: "mysql://root:root_password@127.0.0.1:3306/ecotask_test"
        run: |
          until mysqladmin ping -h 127.0.0.1 -u root -proot_password --silent; do
            echo "Waiting for MySQL..."
            sleep 2
          done

          mkdir -p var
          php bin/console doctrine:database:create --env=test --if-not-exists
          php bin/console doctrine:migrations:migrate --env=test --no-interaction
          php bin/console doctrine:fixtures:load --env=test --no-interaction

      - name: 🚀 Start Application Server
        run: |
          echo "🚀 Démarrage du serveur de test..."
          php -S localhost:8080 -t public/ &
          SERVER_PID=$!
          echo "SERVER_PID=$SERVER_PID" >> $GITHUB_ENV
          
          # Wait for server to start
          sleep 5
          
          # Test server is running
          curl -f http://localhost:8080/health || exit 1
          echo "✅ Serveur démarré avec succès"

      - name: 📝 Create Artillery test configuration
        run: |
          mkdir -p tests/performance
          
          cat > tests/performance/load-test.yml << 'EOF'
          config:
            target: 'http://localhost:8080'
            phases:
              - duration: ${{ env.TEST_DURATION }}
                arrivalRate: ${{ env.CONCURRENT_USERS }}
                name: "Load test"
            defaults:
              headers:
                User-Agent: "Artillery Performance Test"
            processor: "./test-functions.js"
          
          scenarios:
            - name: "Homepage and Dashboard"
              weight: 40
              flow:
                - get:
                    url: "/"
                    expect:
                      - statusCode: 200
                - think: 2
                - get:
                    url: "/dashboard"
                    expect:
                      - statusCode: 200
            
            - name: "Task Management"
              weight: 30
              flow:
                - get:
                    url: "/task/"
                    expect:
                      - statusCode: 200
                - think: 1
                - get:
                    url: "/task/new"
                    expect:
                      - statusCode: 200
            
            - name: "Project Management"
              weight: 20
              flow:
                - get:
                    url: "/project/"
                    expect:
                      - statusCode: 200
                - think: 1
                - get:
                    url: "/project/new"
                    expect:
                      - statusCode: 200
            
            - name: "Health Check"
              weight: 10
              flow:
                - get:
                    url: "/health"
                    expect:
                      - statusCode: 200
                      - hasHeader: "content-type"
          EOF

      - name: 📝 Create Artillery test functions
        run: |
          cat > tests/performance/test-functions.js << 'EOF'
          module.exports = {
            logResponse: function(requestParams, response, context, ee, next) {
              if (response.statusCode >= 400) {
                console.log(`Error ${response.statusCode} for ${requestParams.url}`);
              }
              return next();
            },
            
            setRandomDelay: function(context, events, done) {
              context.vars.delay = Math.floor(Math.random() * 3) + 1;
              return done();
            }
          };
          EOF

      - name: ⚡ Run Artillery Load Tests
        run: |
          echo "⚡ Exécution des tests de charge Artillery..."
          mkdir -p var/reports
          
          # Run Artillery with detailed reporting
          artillery run tests/performance/load-test.yml \
            --output var/reports/artillery-report.json \
            --quiet || ARTILLERY_EXIT_CODE=$?
          
          # Generate HTML report
          artillery report var/reports/artillery-report.json \
            --output var/reports/artillery-report.html || true
          
          echo "ARTILLERY_EXIT_CODE=${ARTILLERY_EXIT_CODE:-0}" >> $GITHUB_ENV

      - name: 📊 Analyze Performance Results
        run: |
          echo "📊 Analyse des résultats de performance..."
          
          if [ -f "var/reports/artillery-report.json" ]; then
            # Extract key metrics using jq
            TOTAL_REQUESTS=$(jq '.aggregate.counters["http.requests"] // 0' var/reports/artillery-report.json)
            TOTAL_RESPONSES=$(jq '.aggregate.counters["http.responses"] // 0' var/reports/artillery-report.json)
            ERROR_RATE=$(jq '.aggregate.counters["http.codes.400"] // 0 + .aggregate.counters["http.codes.500"] // 0' var/reports/artillery-report.json)
            
            RESPONSE_TIME_P50=$(jq '.aggregate.latency.p50 // 0' var/reports/artillery-report.json)
            RESPONSE_TIME_P95=$(jq '.aggregate.latency.p95 // 0' var/reports/artillery-report.json)
            RESPONSE_TIME_P99=$(jq '.aggregate.latency.p99 // 0' var/reports/artillery-report.json)
            
            echo "📊 Métriques de performance:"
            echo "- Requêtes totales: $TOTAL_REQUESTS"
            echo "- Réponses totales: $TOTAL_RESPONSES"
            echo "- Erreurs: $ERROR_RATE"
            echo "- Temps de réponse P50: ${RESPONSE_TIME_P50}ms"
            echo "- Temps de réponse P95: ${RESPONSE_TIME_P95}ms"
            echo "- Temps de réponse P99: ${RESPONSE_TIME_P99}ms"
            
            # Performance thresholds
            if (( $(echo "$RESPONSE_TIME_P95 > 2000" | bc -l) )); then
              echo "::warning::Temps de réponse P95 élevé: ${RESPONSE_TIME_P95}ms (seuil: 2000ms)"
            fi
            
            if (( $(echo "$ERROR_RATE > 0" | bc -l) )); then
              echo "::warning::Erreurs détectées: $ERROR_RATE"
            fi
            
            # Store metrics in environment
            echo "TOTAL_REQUESTS=$TOTAL_REQUESTS" >> $GITHUB_ENV
            echo "RESPONSE_TIME_P95=$RESPONSE_TIME_P95" >> $GITHUB_ENV
            echo "ERROR_RATE=$ERROR_RATE" >> $GITHUB_ENV
          else
            echo "⚠️ Rapport Artillery non trouvé"
          fi

      - name: 📊 Generate Performance Summary
        if: always()
        run: |
          echo "📊 Génération du résumé de performance..."
          
          cat > performance-summary.md << 'EOF'
          # ⚡ Résumé des Tests de Performance - $(date)
          
          ## 🎯 Configuration
          - **URL cible**: ${{ env.TARGET_URL }}
          - **Durée**: ${{ env.TEST_DURATION }}s
          - **Utilisateurs simultanés**: ${{ env.CONCURRENT_USERS }}
          
          ## 📊 Résultats
          EOF
          
          if [ -n "${{ env.TOTAL_REQUESTS }}" ]; then
            echo "- **Requêtes totales**: ${{ env.TOTAL_REQUESTS }}" >> performance-summary.md
            echo "- **Temps de réponse P95**: ${{ env.RESPONSE_TIME_P95 }}ms" >> performance-summary.md
            echo "- **Erreurs**: ${{ env.ERROR_RATE }}" >> performance-summary.md
            
            if [ "${{ env.ARTILLERY_EXIT_CODE }}" = "0" ]; then
              echo "- **Statut**: ✅ Succès" >> performance-summary.md
            else
              echo "- **Statut**: ❌ Échec" >> performance-summary.md
            fi
          else
            echo "- **Statut**: ⚠️ Données non disponibles" >> performance-summary.md
          fi

      - name: 📤 Upload Performance Reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: performance-reports
          path: |
            var/reports/
            performance-summary.md
            tests/performance/
          retention-days: 30

      - name: 🛑 Stop Application Server
        if: always()
        run: |
          if [ -n "${{ env.SERVER_PID }}" ]; then
            kill ${{ env.SERVER_PID }} || true
          fi

      - name: 📢 Performance Notification
        if: always()
        run: |
          if [ "${{ env.ARTILLERY_EXIT_CODE }}" = "0" ]; then
            MESSAGE="⚡ *Tests de Performance - SUCCÈS*%0A%0A"
            MESSAGE="${MESSAGE}✅ Tests de charge terminés avec succès%0A"
          else
            MESSAGE="⚡ *Tests de Performance - ÉCHEC*%0A%0A"
            MESSAGE="${MESSAGE}❌ Des problèmes de performance détectés%0A"
          fi
          
          if [ -n "${{ env.TOTAL_REQUESTS }}" ]; then
            MESSAGE="${MESSAGE}%0A📊 *Métriques*:%0A"
            MESSAGE="${MESSAGE}🔢 Requêtes: ${{ env.TOTAL_REQUESTS }}%0A"
            MESSAGE="${MESSAGE}⏱️ P95: ${{ env.RESPONSE_TIME_P95 }}ms%0A"
            MESSAGE="${MESSAGE}❌ Erreurs: ${{ env.ERROR_RATE }}%0A"
          fi
          
          MESSAGE="${MESSAGE}%0A🔗 *Workflow*: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
          
          # Send notification if secrets are available
          if [ -n "${{ secrets.TELEGRAM_BOT_TOKEN }}" ] && [ -n "${{ secrets.TELEGRAM_CHAT_ID }}" ]; then
            curl -s -X POST "https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage" \
              -d "chat_id=${{ secrets.TELEGRAM_CHAT_ID }}" \
              -d "text=${MESSAGE}" \
              -d "parse_mode=Markdown" \
              -d "disable_web_page_preview=true" || echo "Notification Telegram échouée"
          else
            echo "Secrets Telegram non configurés, notification ignorée"
          fi
