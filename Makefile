# Makefile pour EcoTask
.PHONY: help build up down restart logs shell test clean deploy

# Variables
DOCKER_COMPOSE = sudo docker-compose
DOCKER_COMPOSE_PROD = sudo docker-compose -f docker-compose.prod.yml
APP_CONTAINER = ecotask_app

# Couleurs pour l'affichage
GREEN = \033[0;32m
YELLOW = \033[1;33m
RED = \033[0;31m
NC = \033[0m # No Color

## Affiche cette aide
help:
	@echo "$(GREEN)EcoTask - Commandes disponibles:$(NC)"
	@echo ""
	@echo "$(YELLOW)Développement:$(NC)"
	@echo "  make build          - Construire les images Docker"
	@echo "  make up             - Démarrer les services"
	@echo "  make down           - Arrêter les services"
	@echo "  make restart        - Redémarrer les services"
	@echo "  make logs           - Afficher les logs"
	@echo "  make shell          - Accéder au shell de l'application"
	@echo ""
	@echo "$(YELLOW)Base de données:$(NC)"
	@echo "  make db-create      - Créer la base de données"
	@echo "  make db-migrate     - Exécuter les migrations"
	@echo "  make db-fixtures    - Charger les fixtures"
	@echo "  make db-reset       - Réinitialiser la base de données"
	@echo ""
	@echo "$(YELLOW)Tests et qualité:$(NC)"
	@echo "  make test           - Exécuter les tests"
	@echo "  make test-coverage  - Tests avec couverture de code"
	@echo "  make cs-fix         - Corriger le style de code"
	@echo "  make phpstan        - Analyse statique avec PHPStan"
	@echo ""
	@echo "$(YELLOW)Production:$(NC)"
	@echo "  make deploy-prod    - Déployer en production"
	@echo "  make backup         - Sauvegarder la base de données"
	@echo ""
	@echo "$(YELLOW)Maintenance:$(NC)"
	@echo "  make clean          - Nettoyer Docker"
	@echo "  make clean-all      - Nettoyage complet"

## Construire les images Docker
build:
	@echo "$(GREEN)Construction des images Docker...$(NC)"
	$(DOCKER_COMPOSE) build

## Démarrer les services
up:
	@echo "$(GREEN)Démarrage des services...$(NC)"
	$(DOCKER_COMPOSE) up -d
	@echo "$(GREEN)Services démarrés!$(NC)"
	@echo "Application: http://localhost:8080"
	@echo "Adminer: http://localhost:8081"
	@echo "MailHog: http://localhost:8025"

## Arrêter les services
down:
	@echo "$(YELLOW)Arrêt des services...$(NC)"
	$(DOCKER_COMPOSE) down

## Redémarrer les services
restart: down up

## Afficher les logs
logs:
	$(DOCKER_COMPOSE) logs -f

## Accéder au shell de l'application
shell:
	$(DOCKER_COMPOSE) exec app sh

## Créer la base de données
db-create:
	@echo "$(GREEN)Création de la base de données...$(NC)"
	$(DOCKER_COMPOSE) exec app php bin/console doctrine:database:create --if-not-exists

## Exécuter les migrations
db-migrate:
	@echo "$(GREEN)Exécution des migrations...$(NC)"
	$(DOCKER_COMPOSE) exec app php bin/console doctrine:migrations:migrate --no-interaction

## Charger les fixtures
db-fixtures:
	@echo "$(GREEN)Chargement des fixtures...$(NC)"
	$(DOCKER_COMPOSE) exec app php bin/console doctrine:fixtures:load --no-interaction

## Réinitialiser la base de données
db-reset:
	@echo "$(YELLOW)Réinitialisation de la base de données...$(NC)"
	$(DOCKER_COMPOSE) exec app php bin/console doctrine:database:drop --force --if-exists
	$(DOCKER_COMPOSE) exec app php bin/console doctrine:database:create
	$(DOCKER_COMPOSE) exec app php bin/console doctrine:migrations:migrate --no-interaction
	$(DOCKER_COMPOSE) exec app php bin/console doctrine:fixtures:load --no-interaction

## Exécuter les tests
test:
	@echo "$(GREEN)Exécution des tests...$(NC)"
	$(DOCKER_COMPOSE) exec app php bin/phpunit

## Tests avec couverture de code
test-coverage:
	@echo "$(GREEN)Tests avec couverture de code...$(NC)"
	$(DOCKER_COMPOSE) exec app php bin/phpunit --coverage-html var/coverage --coverage-clover var/coverage/clover.xml

## Vérifier le seuil de couverture de code
test-coverage-check:
	@echo "$(GREEN)Vérification du seuil de couverture...$(NC)"
	$(DOCKER_COMPOSE) exec app php bin/phpunit --coverage-clover var/coverage/clover.xml
	$(DOCKER_COMPOSE) exec app php scripts/check-coverage.php

## Tests complets avec couverture et vérification
test-full:
	@echo "$(GREEN)Tests complets avec couverture...$(NC)"
	$(DOCKER_COMPOSE) exec app php bin/phpunit --coverage-html var/coverage --coverage-clover var/coverage/clover.xml --coverage-text
	$(DOCKER_COMPOSE) exec app php scripts/check-coverage.php

## Corriger le style de code
cs-fix:
	@echo "$(GREEN)Correction du style de code...$(NC)"
	$(DOCKER_COMPOSE) exec app vendor/bin/php-cs-fixer fix

## Vérifier le style de code (dry-run)
cs-check:
	@echo "$(GREEN)Vérification du style de code...$(NC)"
	$(DOCKER_COMPOSE) exec app vendor/bin/php-cs-fixer fix --dry-run --diff

## Analyse statique avec PHPStan
phpstan:
	@echo "$(GREEN)Analyse statique avec PHPStan...$(NC)"
	$(DOCKER_COMPOSE) exec app vendor/bin/phpstan analyse --memory-limit=1G

## Audit de sécurité
security-check:
	@echo "$(GREEN)Audit de sécurité...$(NC)"
	$(DOCKER_COMPOSE) exec app composer audit

## Installer les dépendances
install:
	@echo "$(GREEN)Installation des dépendances...$(NC)"
	$(DOCKER_COMPOSE) exec app composer install
	$(DOCKER_COMPOSE) run --rm node npm install

## Déployer en production
deploy-prod:
	@echo "$(GREEN)Déploiement en production...$(NC)"
	chmod +x scripts/deploy.sh
	./scripts/deploy.sh production

## Sauvegarder la base de données
backup:
	@echo "$(GREEN)Sauvegarde de la base de données...$(NC)"
	mkdir -p backups
	$(DOCKER_COMPOSE) exec db mysqldump -u ecotask -pecotask_password ecotask_db > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql

## Nettoyer Docker
clean:
	@echo "$(YELLOW)Nettoyage Docker...$(NC)"
	docker system prune -f
	docker image prune -f

## Nettoyage complet
clean-all:
	@echo "$(RED)Nettoyage complet (ATTENTION: supprime tout)...$(NC)"
	$(DOCKER_COMPOSE) down -v --rmi all
	docker system prune -af --volumes

## Vérifier la santé de l'application
health:
	@echo "$(GREEN)Vérification de la santé...$(NC)"
	curl -f http://localhost:8080/health || echo "$(RED)Application non accessible$(NC)"

## Afficher les statistiques Docker
stats:
	docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"

## Première installation complète
install-full: build up db-create db-migrate db-fixtures
	@echo "$(GREEN)Installation complète terminée!$(NC)"
	@echo "Application disponible sur: http://localhost:8080"

## Démarrage rapide avec initialisation
quick-start:
	@echo "$(GREEN)Démarrage rapide EcoTask...$(NC)"
	$(DOCKER_COMPOSE) -f docker-compose.test.yml up -d
	./scripts/init-database.sh test
	@echo "$(GREEN)EcoTask prêt!$(NC)"

## Démarrage développement complet
dev-start:
	@echo "$(GREEN)Démarrage environnement de développement...$(NC)"
	$(DOCKER_COMPOSE) up -d app db redis adminer mailhog
	./scripts/init-database.sh dev
	@echo "$(GREEN)Environnement de développement prêt!$(NC)"

## Nettoyage et redémarrage
clean-restart: clean-docker quick-start

## Nettoyage Docker
clean-docker:
	@echo "$(YELLOW)Nettoyage Docker...$(NC)"
	./scripts/cleanup-docker.sh

## Pipeline CI complet
ci-pipeline: cs-check phpstan security-check test
	@echo "$(GREEN)Pipeline CI terminé avec succès!$(NC)"

## Préparation pour commit
pre-commit: cs-fix phpstan test
	@echo "$(GREEN)Code prêt pour commit!$(NC)"

## Installation des outils de développement
install-dev-tools:
	@echo "$(GREEN)Installation des outils de développement...$(NC)"
	$(DOCKER_COMPOSE) exec app composer install --dev
	@echo "$(GREEN)Outils installés: PHP CS Fixer, PHPStan, PHPUnit$(NC)"
