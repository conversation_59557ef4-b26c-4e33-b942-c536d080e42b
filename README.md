test deploy
# 🌱 EcoTask - Gestion de tâches écologique

[![CI/CD Status](https://github.com/sami53tk/EcoTask/actions/workflows/ci.yml/badge.svg)](https://github.com/sami53tk/EcoTask/actions)
[![Deploy Status](https://github.com/sami53tk/EcoTask/actions/workflows/deploy.yml/badge.svg)](https://github.com/sami53tk/EcoTask/actions)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue)](https://github.com/sami53tk/EcoTask/pkgs/container/ecotask)

**Application de gestion de tâches avec calcul d'émissions CO2 - 100% Conteneurisée avec CI/CD complet**

> 🚀 **Test de déploiement automatique en cours** - Version mise à jour le 24 Juin 2025

## 🎯 **Vue d'ensemble**

EcoTask est une application web moderne de gestion de tâches qui intègre le calcul automatique des émissions CO2. Développée avec Symfony 7.3 et entièrement conteneurisée avec Docker, elle dispose d'un pipeline CI/CD complet avec GitHub Actions.

### ✨ **Fonctionnalités**

- 📋 **Gestion de tâches** : Création, modification, suivi avec priorités et statuts
- 🌍 **Calcul CO2** : Estimation automatique des émissions selon le type de tâche
- 📊 **Dashboard** : Statistiques et visualisations des données
- 🎨 **Interface moderne** : Design responsive avec Tailwind CSS
- 🔒 **API REST** : Endpoints sécurisés avec health checks

### 🛠️ **Technologies**

- **Backend** : PHP 8.3, Symfony 7.3, Doctrine ORM
- **Frontend** : Twig, Tailwind CSS, JavaScript
- **Base de données** : MySQL 8.0
- **Cache** : Redis 7
- **Conteneurisation** : Docker, Docker Compose
- **CI/CD** : GitHub Actions
- **Qualité** : PHPStan, PHP CS Fixer, PHPUnit

## 🚀 **Démarrage Rapide**

### **Prérequis**
- Docker & Docker Compose
- Git

### **Installation**
```bash
# 1. Cloner le projet
git clone https://github.com/sami53tk/EcoTask.git
cd EcoTask

# 2. Démarrage rapide
make quick-start

# 3. Accéder à l'application
open http://localhost:8080
```

### **Commandes Utiles**
```bash
# Développement
make dev-start          # Démarrage complet avec hot-reload
make logs               # Voir les logs en temps réel
make shell              # Accès shell au conteneur

# Tests et Qualité
make test               # Tests unitaires
make cs-fix             # Correction style de code
make phpstan            # Analyse statique
make ci-pipeline        # Pipeline complet

# Maintenance
make clean-docker       # Nettoyage Docker
make backup-db          # Sauvegarde base de données
```

## 📊 **Statut du Projet**

### ✅ **Pipeline CI/CD**
- **Tests automatisés** : 7 tests, 25 assertions, 100% réussite
- **Qualité de code** : PHPStan niveau 8/8, 0 erreur
- **Style de code** : 21/21 fichiers conformes PSR-12
- **Sécurité** : 0 vulnérabilité détectée
- **Build Docker** : Images multi-stage optimisées

### 🎯 **Environnements**
- **Développement** : http://localhost:8080
- **Staging** : Déploiement automatique sur main
- **Production** : Déploiement contrôlé par tags

### 📈 **Métriques**
- **Temps de réponse** : <200ms
- **Démarrage** : ~30 secondes
- **Pipeline CI** : ~12-15 minutes
- **Disponibilité** : 100%

## 📚 **Documentation**

### **Guides Principaux**
- 📖 [Guide CI/CD](CI_CD_README.md) - Pipeline complet
- 🐳 [Guide Docker](DOCKER_GUIDE.md) - Conteneurisation
- 🔧 [Aide-mémoire Docker](DOCKER_CHEATSHEET.md) - Commandes utiles
- 📋 [Rapport de livraison](DELIVERY_REPORT.md) - Résultats obtenus

### **Intégrations**
- 📢 [Slack Integration](SLACK_INTEGRATION.md) - Notifications
- 🔒 [Sécurité](docs/security.md) - Bonnes pratiques
- 📊 [Monitoring](docs/monitoring.md) - Surveillance

## 🔄 **Workflow de Développement**

### **Développement Local**
```bash
# 1. Créer une branche
git checkout -b feature/ma-fonctionnalite

# 2. Développer avec hot-reload
make dev-start

# 3. Valider avant commit
make pre-commit

# 4. Commit et push
git add . && git commit -m "feat: nouvelle fonctionnalité"
git push origin feature/ma-fonctionnalite
```

### **Release Production**
```bash
# 1. Merge vers main (staging automatique)
git checkout main && git merge feature/ma-fonctionnalite

# 2. Tag de version (production automatique)
git tag -a v1.2.0 -m "Release v1.2.0"
git push origin v1.2.0
```

## 🏗️ **Architecture**

### **Structure du Projet**
```
EcoTask/
├── src/                    # Code source Symfony
├── templates/              # Templates Twig
├── public/                 # Assets publics
├── docker/                 # Configuration Docker
├── .github/workflows/      # GitHub Actions
├── scripts/                # Scripts d'automatisation
├── tests/                  # Tests unitaires
└── docs/                   # Documentation
```

### **Services Docker**
- **app** : Application Symfony (PHP 8.3 + Nginx)
- **db** : Base de données MySQL 8.0
- **redis** : Cache Redis 7
- **adminer** : Interface DB (développement)

## 🤝 **Contribution**

### **Standards de Code**
- **PSR-12** : Style de code PHP
- **PHPStan niveau 8** : Analyse statique
- **Tests unitaires** : Couverture obligatoire
- **Documentation** : Commentaires et guides

### **Process de Contribution**
1. Fork du projet
2. Branche feature
3. Tests et qualité validés
4. Pull Request avec description
5. Review et merge

## 📄 **Licence**

Ce projet est sous licence MIT. Voir [LICENSE](LICENSE) pour plus de détails.

## 👥 **Équipe**

- **Développement** : Équipe Full Stack M1
- **DevOps** : Pipeline CI/CD automatisé
- **Qualité** : Standards industriels appliqués

---

**🌱 EcoTask - Pour une gestion de tâches respectueuse de l'environnement**
# 🌱 EcoTask - Gestionnaire de Projets Écologiques

Application Symfony de gestion de projets et tâches écologiques avec CI/CD automatique.

## 🚀 Statut du Projet

✅ **CI/CD Automatique Opérationnel**
✅ **Déploiement Production Fonctionnel**
✅ **Tests Automatisés**
✅ **Docker & Portainer Intégrés**

## 🏗️ Architecture

### Stack Technique
- **Backend** : Symfony 7.3 (PHP 8.3)
- **Base de données** : MySQL 8.0
- **Cache** : Redis 7
- **Serveur web** : Nginx
- **Conteneurisation** : Docker & Docker Compose
- **Proxy** : Nginx Proxy Manager
- **Gestion** : Portainer

### CI/CD
- **Tests automatisés** : PHPUnit, PHPStan, Docker
- **Déploiement automatique** : GitHub Actions
- **Workflow** : Push → Tests → Production

## 🚀 Déploiement

### Prérequis Serveur
- Docker & Docker Compose
- Portainer (optionnel)
- Nginx Proxy Manager (optionnel)

### Déploiement Automatique
```bash
# Le CI/CD se déclenche automatiquement sur push main
git push origin main
```

### Déploiement Manuel
```bash
# Cloner le projet
git clone https://github.com/sami53tk/EcoTask.git
cd EcoTask

# Configurer l'environnement
cp .env.production .env

# Déployer avec Docker
chmod +x scripts/deploy-docker.sh
./scripts/deploy-docker.sh production
```

## 🧪 Tests

```bash
# Tests PHP
composer test

# Tests Docker
docker build -f Dockerfile.test -t ecotask:test .

# Tests complets
make test
```

## 📊 Monitoring

EcoTask dispose d'un système de monitoring complet avec notifications Telegram intelligentes.

### 🤖 Notifications Telegram

- **Déploiements** : Notifications automatiques de succès/échec
- **Health Checks** : Rapports de santé détaillés toutes les heures
- **Alertes** : Notifications critiques en temps réel
- **Métriques** : Informations système et performance

### 🔧 Configuration

```bash
# Configuration automatique
./scripts/setup-monitoring.sh

# Test manuel
php scripts/telegram-monitor.php health
```

### 📋 Secrets GitHub requis

- `TELEGRAM_BOT_TOKEN` : Token du bot Telegram
- `TELEGRAM_CHAT_ID` : ID du chat pour les notifications

### 🚨 Types de notifications

1. **Déploiement réussi** : Confirmation avec détails
2. **Déploiement échoué** : Alerte avec logs d'erreur
3. **Rapport de santé** : État de l'application et métriques
4. **Alertes critiques** : Problèmes nécessitant une intervention

### 🌐 URLs de monitoring

- **URL Production** : https://ecotask.sami-rochdi.fr
- **Health Check** : https://ecotask.sami-rochdi.fr/health
- **Portainer** : http://server:9000
- **Logs** : `docker logs ecotask-app`

## 🔧 Maintenance

```bash
# Voir les conteneurs
docker ps | grep ecotask

# Redémarrer l'application
docker restart ecotask-app

# Voir les logs
docker logs ecotask-app --follow

# Backup base de données
docker exec ecotask-db mysqldump -u ecotask -p ecotask_db > backup.sql
```
# CI/CD Test - Tue Jun 24 19:09:48 UTC 2025

verif mis a jour
