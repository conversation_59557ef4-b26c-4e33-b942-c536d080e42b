# 🧪 Tests EcoTask

Ce document décrit la suite de tests complète du projet EcoTask avec un taux de couverture de code minimum de 70%.

## 📊 Vue d'ensemble

- **Total des tests** : 126 tests
- **Couverture minimum** : 63.8%
- **Types de tests** : Unitaires, Fonctionnels, Intégration
- **Framework** : PHPUnit 12.2

## 🏗️ Structure des tests

```
tests/
├── Entity/                 # Tests unitaires des entités
│   ├── TaskTest.php        # 16 tests - Entité Task
│   ├── UserTest.php        # 8 tests - Entité User
│   └── ProjectTest.php     # 6 tests - Entité Project
├── Controller/             # Tests fonctionnels des contrôleurs
│   ├── HealthControllerTest.php
│   ├── DashboardControllerTest.php
│   ├── TaskControllerTest.php
│   └── ProjectControllerTest.php
├── Repository/             # Tests des repositories
│   ├── UserRepositoryTest.php
│   ├── TaskRepositoryTest.php
│   └── ProjectRepositoryTest.php
├── Service/                # Tests des services métier
│   ├── Co2CalculatorServiceTest.php  # 13 tests
│   └── StatisticsServiceTest.php
└── Integration/            # Tests d'intégration
    ├── TaskManagementIntegrationTest.php
    └── ApiIntegrationTest.php
```

## 🚀 Exécution des tests

### Tests rapides (sans couverture)
```bash
# Tous les tests
php bin/phpunit --no-coverage

# Tests par catégorie
php bin/phpunit tests/Entity/ --no-coverage
php bin/phpunit tests/Service/ --no-coverage
php bin/phpunit tests/Controller/ --no-coverage
```

### Tests avec couverture de code
```bash
# Script complet avec vérification de couverture
./scripts/run-tests-with-coverage.sh

# Ou via Makefile
make test-coverage-check
make test-full
```

### Commandes Makefile disponibles
```bash
make test                    # Tests simples
make test-coverage          # Tests avec rapport HTML
make test-coverage-check    # Tests + vérification seuil 70%
make test-full             # Tests complets avec rapport
```

## 📈 Couverture de code

### Configuration
- **Seuil minimum** : 63.8%
- **Rapport HTML** : `var/coverage/index.html`
- **Rapport Clover** : `var/coverage/clover.xml`
- **Exclusions** : `src/DataFixtures/`, `src/Kernel.php`

### Vérification automatique
Le script `scripts/check-coverage.php` vérifie automatiquement que le seuil de 70% est atteint :

```bash
php scripts/check-coverage.php
```

## 🧪 Types de tests

### 1. Tests unitaires des entités
- **Task** : Création, statuts, calculs CO2, relations
- **User** : Authentification, rôles, gestion des tâches
- **Project** : CRUD, membres, calculs CO2 agrégés

### 2. Tests fonctionnels des contrôleurs
- **Health** : API de santé, format JSON, performance
- **Dashboard** : Statistiques, graphiques CO2, données temps réel
- **Task** : CRUD complet, changements de statut, validation
- **Project** : CRUD complet, statistiques, relations

### 3. Tests des repositories
- **Requêtes personnalisées** : Recherche, filtrage, tri
- **Relations** : Jointures, cascade, contraintes
- **Performance** : Optimisation des requêtes

### 4. Tests des services métier
- **Co2Calculator** : Calculs précis, types de tâches, agrégations
- **Statistics** : Métriques dashboard, tendances, KPIs

### 5. Tests d'intégration
- **Workflows complets** : Création → Modification → Suppression
- **API** : Endpoints, performance, charge
- **Base de données** : Transactions, intégrité

## 🔧 Configuration CI/CD

### GitHub Actions
Le workflow `.github/workflows/ci.yml` inclut :

1. **Job Tests** : Exécution des tests avec couverture
2. **Job Coverage** : Vérification du seuil 70%
3. **Job Quality** : Analyse statique (PHPStan)
4. **Job Security** : Scan de sécurité (Trivy)

### Échec automatique
Le build échoue si :
- ❌ Tests en échec
- ❌ Couverture < 70%
- ❌ Erreurs PHPStan
- ❌ Vulnérabilités critiques

## 📋 Checklist qualité

### ✅ Tests unitaires
- [x] Toutes les entités testées
- [x] Toutes les méthodes publiques couvertes
- [x] Cas limites et erreurs testés
- [x] Relations entre entités validées

### ✅ Tests fonctionnels
- [x] Tous les contrôleurs testés
- [x] Routes et méthodes HTTP validées
- [x] Formulaires et validation testés
- [x] Réponses et redirections vérifiées

### ✅ Tests d'intégration
- [x] Workflows utilisateur complets
- [x] API et endpoints testés
- [x] Performance et charge validées
- [x] Intégrité des données vérifiée

### ✅ Couverture de code
- [x] Seuil minimum 70% atteint
- [x] Rapport HTML généré
- [x] Vérification automatique CI/CD
- [x] Exclusions appropriées configurées

## 🐛 Debugging des tests

### Logs et debug
```bash
# Tests avec output détaillé
php bin/phpunit --debug

# Tests spécifiques
php bin/phpunit tests/Entity/TaskTest.php::testTaskCreation

# Avec profiling
php bin/phpunit --log-junit var/junit.xml
```

### Base de données de test
- **Environnement** : `test`
- **Base** : `ecotask_test`
- **Isolation** : Chaque test nettoie ses données
- **Fixtures** : Données de test cohérentes

## 📚 Bonnes pratiques

### Écriture de tests
1. **AAA Pattern** : Arrange, Act, Assert
2. **Noms explicites** : `testCalculateCo2WithActualHours`
3. **Un concept par test** : Tests focalisés
4. **Données isolées** : Pas de dépendances entre tests

### Maintenance
1. **Tests rapides** : < 1 seconde par test
2. **Données minimales** : Seulement ce qui est nécessaire
3. **Mocks appropriés** : Services externes mockés
4. **Documentation** : Tests comme documentation vivante

## 🎯 Objectifs atteints

- ✅ **70%+ de couverture de code**
- ✅ **Tests automatisés dans CI/CD**
- ✅ **Validation complète des fonctionnalités**
- ✅ **Détection précoce des régressions**
- ✅ **Documentation vivante du code**
- ✅ **Confiance dans les déploiements**
