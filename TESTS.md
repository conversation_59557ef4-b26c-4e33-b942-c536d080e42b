# 🧪 Documentation des Tests - EcoTask

## 📋 Vue d'ensemble

Ce document décrit la stratégie de tests complète mise en place pour le projet EcoTask, garantissant une couverture de code minimum de **80%** et un monitoring détaillé des échecs.

## 🎯 Objectifs

- ✅ **Couverture minimum 80%** : Seuil enforcer automatiquement
- ✅ **Tests multi-niveaux** : Unitaires, intégration, performance
- ✅ **Monitoring détaillé** : Identification précise des échecs
- ✅ **CI/CD robuste** : Pipeline avec rapports complets
- ✅ **Qualité du code** : Analyse statique et sécurité

## 📁 Structure des Tests

```
tests/
├── Entity/                 # Tests unitaires des entités
│   ├── TaskTest.php        # Tests de l'entité Task (20+ méthodes)
│   ├── ProjectTest.php     # Tests de l'entité Project (15+ méthodes)
│   └── UserTest.php        # Tests de l'entité User (15+ méthodes)
├── Repository/             # Tests des repositories
│   ├── TaskRepositoryTest.php
│   ├── ProjectRepositoryTest.php
│   └── UserRepositoryTest.php
├── Controller/             # Tests d'intégration des contrôleurs
│   ├── HealthControllerTest.php
│   ├── DashboardControllerTest.php
│   ├── TaskControllerTest.php
│   └── ProjectControllerTest.php
├── Form/                   # Tests des formulaires
│   ├── TaskTypeTest.php
│   └── ProjectTypeTest.php
├── DataFixtures/           # Tests des fixtures
│   └── AppFixturesTest.php
└── Performance/            # Tests de performance
    └── PerformanceTest.php
```

## 🧪 Types de Tests

### 1. Tests Unitaires (Entity, Repository, Form)

**Couverture** : Toutes les méthodes publiques et logique métier

**Exemples** :
- Validation des entités et leurs relations
- Calculs CO2 et méthodes métier
- Requêtes de base de données
- Validation des formulaires

### 2. Tests d'Intégration (Controller)

**Couverture** : Tous les endpoints et workflows

**Exemples** :
- Navigation et rendu des pages
- Soumission de formulaires
- Gestion des erreurs HTTP
- Tests de sécurité

### 3. Tests de Performance

**Couverture** : Composants critiques et charges

**Exemples** :
- Performance du dashboard avec gros datasets
- Temps de réponse des endpoints
- Tests de charge avec Artillery
- Optimisation des requêtes

## ⚙️ Configuration

### PHPUnit (phpunit.dist.xml)

```xml
<!-- Seuils de couverture enforcer -->
<thresholds>
    <coverage minCoverage="80" includeUncoveredFiles="true"/>
    <line minCoverage="80"/>
    <method minCoverage="80"/>
    <class minCoverage="80"/>
</thresholds>

<!-- Rapports multiples -->
<report>
    <clover outputFile="var/coverage/clover.xml"/>
    <html outputDirectory="var/coverage/html"/>
    <text outputFile="var/coverage/coverage.txt"/>
</report>
```

### Suites de Tests

- **Unit Tests** : Entity, Repository, Form, DataFixtures
- **Integration Tests** : Controller
- **All Tests** : Tous les tests

## 🚀 Exécution des Tests

### Commandes Principales

```bash
# Installation et validation
composer install
./scripts/validate-tests.sh

# Exécution complète
./run-tests.sh

# Tests spécifiques
php bin/phpunit --testsuite="Unit Tests"
php bin/phpunit --testsuite="Integration Tests"

# Avec couverture détaillée
php bin/phpunit --coverage-html=var/coverage/html

# Vérification de couverture
php scripts/check-coverage.php
```

### Tests de Performance

```bash
# Tests de performance locaux
php bin/phpunit tests/Performance/

# Tests de charge avec Artillery (CI/CD)
artillery run tests/performance/load-test.yml
```

## 📊 Monitoring et Rapports

### GitHub Actions CI/CD

Le pipeline CI/CD génère des rapports détaillés pour chaque échec :

#### Job 1: Tests PHP
- ✅ Exécution PHPUnit avec couverture
- ✅ Vérification seuil 80% minimum
- ✅ Rapports HTML, XML, texte
- ✅ Upload des artefacts

#### Job 2: Qualité du Code
- ✅ Analyse PHPStan niveau 8
- ✅ Vérification style de code
- ✅ Audit de sécurité
- ✅ Rapports détaillés

#### Job 3: Tests Docker
- ✅ Build multi-stage
- ✅ Tests des images
- ✅ Validation des environnements

#### Job 4: Sécurité
- ✅ Scan Trivy complet
- ✅ Détection vulnérabilités
- ✅ Rapports SARIF

#### Job 5: Consolidation
- ✅ Rapport global unifié
- ✅ Notifications Telegram
- ✅ Statut détaillé par job

### Rapports Générés

```
var/coverage/
├── html/                   # Rapport HTML interactif
├── clover.xml             # Format Codecov
├── coverage.txt           # Résumé textuel
├── junit.xml              # Résultats JUnit
├── testdox.html           # Documentation des tests
└── testdox.txt            # Tests en format lisible
```

### Identification des Échecs

Chaque échec de test fournit :

1. **Test spécifique** qui a échoué
2. **Ligne exacte** du problème
3. **Message d'erreur** détaillé
4. **Contexte** et données de test
5. **Couverture** impactée
6. **Recommandations** d'amélioration

## 📈 Métriques de Qualité

### Couverture Actuelle

- **Instructions** : 80%+ (enforcer)
- **Méthodes** : 80%+ (enforcer)
- **Classes** : 80%+ (enforcer)

### Tests Implémentés

- **Fichiers de test** : 14+
- **Méthodes de test** : 150+
- **Assertions** : 500+

### Performance

- **Dashboard** : < 3s avec gros dataset
- **Endpoints** : < 2s en moyenne
- **Health check** : < 200ms
- **Tests complets** : < 5min

## 🔧 Outils et Technologies

### Framework de Tests
- **PHPUnit 12.2** : Framework principal
- **Symfony WebTestCase** : Tests d'intégration
- **Doctrine Test** : Tests de base de données

### Analyse de Code
- **PHPStan niveau 8** : Analyse statique maximale
- **PHP CS Fixer** : Style de code
- **Composer Audit** : Sécurité des dépendances

### Performance
- **Artillery** : Tests de charge
- **Métriques custom** : Performance des composants
- **Monitoring continu** : Surveillance automatique

### CI/CD
- **GitHub Actions** : Pipeline automatisé
- **Codecov** : Suivi de couverture
- **Trivy** : Scan de sécurité
- **Telegram** : Notifications

## 🎯 Bonnes Pratiques

### Écriture de Tests

1. **Nommage explicite** : `testMethodName_Scenario_ExpectedResult`
2. **Arrange-Act-Assert** : Structure claire
3. **Isolation** : Tests indépendants
4. **Données de test** : Fixtures cohérentes
5. **Assertions précises** : Messages d'erreur clairs

### Maintenance

1. **Tests en premier** : TDD quand possible
2. **Refactoring sûr** : Tests comme filet de sécurité
3. **Documentation** : Tests comme spécification
4. **Performance** : Surveillance continue
5. **Sécurité** : Tests des vulnérabilités

## 🚨 Résolution des Problèmes

### Échec de Couverture

```bash
# Identifier les zones non couvertes
php bin/phpunit --coverage-html=var/coverage/html
# Ouvrir var/coverage/html/index.html

# Vérifier les seuils
php scripts/check-coverage.php
```

### Échec de Tests

```bash
# Tests détaillés avec stack trace
php bin/phpunit --verbose

# Test spécifique
php bin/phpunit tests/Entity/TaskTest.php::testSpecificMethod

# Debug avec var_dump
php bin/phpunit --debug
```

### Performance

```bash
# Profiling des tests lents
php bin/phpunit --log-junit=var/reports/junit.xml

# Tests de performance
php bin/phpunit tests/Performance/
```

## 📞 Support

Pour toute question sur les tests :

1. **Documentation** : Ce fichier et commentaires dans le code
2. **Rapports** : Artefacts GitHub Actions
3. **Logs** : var/coverage/ et var/reports/
4. **Monitoring** : Notifications Telegram configurées

---

**✅ Configuration complète et prête pour la production !**
