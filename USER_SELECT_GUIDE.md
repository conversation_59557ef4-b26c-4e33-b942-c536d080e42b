# Guide d'utilisation - Sélecteur d'utilisateurs EcoTask

## 🎯 Fonctionnalités

Le nouveau composant de sélection d'utilisateurs d'EcoTask offre une interface moderne et intuitive pour :

### ✨ Sélection d'utilisateurs
- **Interface moderne** avec recherche en temps réel
- **Sélection multiple** pour les projets (membres d'équipe)
- **Sélection simple** pour les tâches (assignation)
- **Avatars colorés** générés automatiquement
- **Affichage des emails** pour identifier les utilisateurs

### ➕ Ajout d'utilisateurs
- **Bouton "+" intégré** dans le sélecteur
- **Modal de création** avec formulaire simple
- **Validation en temps réel** des données
- **Intégration API** pour la persistance
- **Mot de passe temporaire** généré automatiquement

### 🔍 Recherche et navigation
- **Recherche instantanée** par nom ou email
- **Navigation clavier** (flèches, Échap, Entrée)
- **Dropdown responsive** avec scroll
- **Fermeture automatique** en cliquant à l'extérieur

## 🚀 Utilisation

### Dans les formulaires de projet
```twig
<!-- Le champ membres utilise automatiquement le nouveau composant -->
{{ form_widget(form.members) }}
```

### Dans les formulaires de tâche
```twig
<!-- Le champ assignedTo utilise automatiquement le nouveau composant -->
{{ form_widget(form.assignedTo) }}
```

### Configuration manuelle
```javascript
// Initialisation manuelle d'un select
const userSelect = new UserSelect(document.getElementById('my-select'), {
    multiple: true,
    allowAdd: true,
    placeholder: 'Choisir des utilisateurs...'
});
```

## 🎨 Interface utilisateur

### Sélection d'utilisateurs
1. **Cliquer** sur le champ pour ouvrir la liste
2. **Taper** pour rechercher un utilisateur
3. **Cliquer** sur un utilisateur pour le sélectionner
4. **Cliquer sur ×** sur un tag pour désélectionner

### Ajout d'un nouvel utilisateur
1. **Cliquer** sur le bouton "+" dans le sélecteur
2. **Remplir** le formulaire (Prénom, Nom, Email)
3. **Cliquer** sur "Ajouter"
4. L'utilisateur est **automatiquement sélectionné**

### Notifications
- **Succès** : Utilisateur ajouté avec mot de passe temporaire
- **Erreur** : Messages d'erreur détaillés
- **Auto-fermeture** après quelques secondes

## 🔧 Configuration technique

### Attributs HTML
```html
<!-- Activation du composant -->
<select data-user-select="true" data-allow-add="true">
    <option value="1" data-email="<EMAIL>">John Doe</option>
</select>
```

### Options JavaScript
```javascript
{
    multiple: false,           // Sélection multiple
    placeholder: 'Texte...',   // Texte de placeholder
    allowAdd: true,            // Permettre l'ajout d'utilisateurs
    addUserUrl: '/api/users',  // URL de l'API pour créer
    searchUrl: '/api/users/search' // URL de recherche
}
```

## 🛠️ API Endpoints

### Créer un utilisateur
```http
POST /api/users
Content-Type: application/json

{
    "firstName": "John",
    "lastName": "Doe", 
    "email": "<EMAIL>"
}
```

**Réponse :**
```json
{
    "success": true,
    "user": {
        "id": 123,
        "name": "John Doe",
        "email": "<EMAIL>",
        "firstName": "John",
        "lastName": "Doe"
    },
    "tempPassword": "TempPass1234"
}
```

### Rechercher des utilisateurs
```http
GET /api/users/search?q=john&limit=10
```

### Lister les utilisateurs
```http
GET /api/users?page=1&limit=20
```

## 🎯 Avantages

### Pour les utilisateurs
- ✅ **Interface intuitive** et moderne
- ✅ **Recherche rapide** d'utilisateurs existants
- ✅ **Ajout facile** de nouveaux utilisateurs
- ✅ **Feedback visuel** immédiat
- ✅ **Responsive** sur mobile et desktop

### Pour les développeurs
- ✅ **Intégration transparente** avec Symfony Forms
- ✅ **API REST** bien structurée
- ✅ **Code modulaire** et réutilisable
- ✅ **Validation côté serveur** et client
- ✅ **Gestion d'erreurs** robuste

## 🔒 Sécurité

- **Validation** des données côté serveur
- **Mots de passe temporaires** sécurisés
- **Protection CSRF** intégrée
- **Validation email** unique
- **Sanitisation** des entrées utilisateur

## 📱 Responsive Design

Le composant s'adapte automatiquement :
- **Desktop** : Interface complète avec tous les éléments
- **Tablet** : Optimisation de l'espacement
- **Mobile** : Interface compacte et tactile

## 🎨 Personnalisation

### Couleurs
Le composant utilise les couleurs EcoTask :
- **Vert éco** : `eco-green-*` pour les éléments principaux
- **Bleu éco** : `eco-blue-*` pour les accents
- **Gris** : Pour les éléments neutres

### Animations
- **Transitions fluides** sur les interactions
- **Effets hover** sur les boutons
- **Animations d'ouverture/fermeture** du dropdown

## 🐛 Dépannage

### Le composant ne s'initialise pas
- Vérifier que `data-user-select="true"` est présent
- S'assurer que le script `user-select.js` est chargé
- Vérifier la console pour les erreurs JavaScript

### L'API ne répond pas
- Vérifier que les routes API sont configurées
- Tester les endpoints avec curl ou Postman
- Vérifier les permissions et l'authentification

### Styles cassés
- S'assurer que Tailwind CSS est chargé
- Vérifier que `ecotask-forms.css` est inclus
- Purger le cache du navigateur

## 📈 Performance

- **Lazy loading** des utilisateurs
- **Debounce** sur la recherche
- **Pagination** pour les grandes listes
- **Cache** des résultats de recherche
- **Optimisation** des requêtes SQL
