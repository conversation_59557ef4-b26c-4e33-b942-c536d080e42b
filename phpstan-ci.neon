# Configuration PHPStan simplifiée pour CI
# Sans dépendances Symfony/Doctrine complexes

parameters:
    level: 0
    paths:
        - src

    # Exclusions
    excludePaths:
        - src/Kernel.php
        - var/*
        - vendor/*

    # Ignore errors courants
    ignoreErrors:
        # Ignore Symfony-related errors
        - '#Call to function method_exists\(\) with .* will always evaluate to true#'
        - '#Access to an undefined property#'
        - '#Call to an undefined method#'
        - '#Cannot call method .* on mixed#'
        - '#Parameter .* of method .* expects .*, mixed given#'

    # Cache
    tmpDir: var/cache/phpstan

    # Memory limit
    memoryLimitFile: .phpstan-memory-limit

    # Basic checks only
    checkMissingIterableValueType: false
    checkGenericClassInNonGenericObjectType: false
    reportUnmatchedIgnoredErrors: false
    checkDynamicProperties: false
    checkUninitializedProperties: false
