<?xml version="1.0" encoding="UTF-8"?>

<!-- https://phpunit.readthedocs.io/en/latest/configuration.html -->
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         colors="true"
         failOnDeprecation="true"
         failOnNotice="true"
         failOnWarning="true"
         bootstrap="tests/bootstrap.php"
         cacheDirectory="var/phpunit-cache"
>
    <php>
        <ini name="display_errors" value="1" />
        <ini name="error_reporting" value="-1" />
        <server name="APP_ENV" value="test" force="true" />
        <server name="APP_SECRET" value="test_secret_key" force="true" />
        <server name="DATABASE_URL" value="mysql://root:MLKqsd002@127.0.0.1:3306/ecotask_test" force="true" />
        <server name="SHELL_VERBOSITY" value="-1" />
    </php>

    <testsuites>
        <testsuite name="Project Test Suite">
            <directory>tests</directory>
        </testsuite>
    </testsuites>

    <source ignoreSuppressionOfDeprecations="true"
            ignoreIndirectDeprecations="true"
            restrictNotices="true"
            restrictWarnings="true"
    >
        <include>
            <directory>src</directory>
        </include>

        <deprecationTrigger>
            <method>Doctrine\Deprecations\Deprecation::trigger</method>
            <method>Doctrine\Deprecations\Deprecation::delegateTriggerToBackend</method>
            <function>trigger_deprecation</function>
        </deprecationTrigger>
    </source>

    <extensions>
    </extensions>

    <coverage includeUncoveredFiles="true"
              pathCoverage="false"
              ignoreDeprecatedCodeUnits="true"
              disableCodeCoverageIgnore="true">
        <report>
            <html outputDirectory="var/coverage" lowUpperBound="50" highLowerBound="80"/>
            <clover outputFile="var/coverage/clover.xml"/>
            <text outputFile="php://stdout" showUncoveredFiles="false"/>
        </report>
    </coverage>
</phpunit>
