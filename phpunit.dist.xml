<?xml version="1.0" encoding="UTF-8"?>

<!-- https://phpunit.readthedocs.io/en/latest/configuration.html -->
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         colors="true"
         failOnDeprecation="true"
         failOnNotice="true"
         failOnWarning="true"
         bootstrap="tests/bootstrap.php"
         cacheDirectory=".phpunit.cache"
         stopOnFailure="false"
         stopOnError="false"
         stopOnIncomplete="false"
         stopOnSkipped="false"
         beStrictAboutOutputDuringTests="true"
         beStrictAboutChangesToGlobalState="true"
         enforceTimeLimit="true"
         defaultTimeLimit="10"
>
    <php>
        <ini name="display_errors" value="1" />
        <ini name="error_reporting" value="-1" />
        <ini name="memory_limit" value="512M" />
        <server name="APP_ENV" value="test" force="true" />
        <server name="APP_SECRET" value="test_secret_key" force="true" />
        <server name="DATABASE_URL" value="mysql://ecotask:hMaPbBKQpy5QK5QYS5hzXL5oO1Jmbj@127.0.0.1:3307/ecotask_test" force="true" />
        <server name="SHELL_VERBOSITY" value="-1" />
        <server name="SYMFONY_PHPUNIT_VERSION" value="12.2" />
        <server name="KERNEL_CLASS" value="App\Kernel" force="true" />
    </php>

    <testsuites>
        <testsuite name="EcoTask Test Suite">
            <directory>tests</directory>
        </testsuite>
    </testsuites>

    <source ignoreSuppressionOfDeprecations="true"
            ignoreIndirectDeprecations="true"
            restrictNotices="true"
            restrictWarnings="true"
    >
        <include>
            <directory>src</directory>
        </include>
        <exclude>
            <file>src/Kernel.php</file>
            <directory>src/DataFixtures</directory>
        </exclude>

        <deprecationTrigger>
            <method>Doctrine\Deprecations\Deprecation::trigger</method>
            <method>Doctrine\Deprecations\Deprecation::delegateTriggerToBackend</method>
            <function>trigger_deprecation</function>
        </deprecationTrigger>
    </source>

    <coverage includeUncoveredFiles="true"
              ignoreDeprecatedCodeUnits="true"
              disableCodeCoverageIgnore="false">
        <report>
            <clover outputFile="var/coverage/clover.xml"/>
            <html outputDirectory="var/coverage/html" lowUpperBound="50" highLowerBound="80"/>
            <text outputFile="var/coverage/coverage.txt" showUncoveredFiles="true" showOnlySummary="false"/>
            <xml outputDirectory="var/coverage/xml"/>
        </report>
    </coverage>

    <logging>
        <junit outputFile="var/coverage/junit.xml"/>
        <testdoxHtml outputFile="var/coverage/testdox.html"/>
        <testdoxText outputFile="var/coverage/testdox.txt"/>
    </logging>

    <extensions>
    </extensions>
</phpunit>
