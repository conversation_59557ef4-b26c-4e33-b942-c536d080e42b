/**
 * EcoTask User Select Component
 * Modern user selection with ability to add new users
 */

class UserSelect {
    constructor(element, options = {}) {
        this.element = element;
        this.options = {
            multiple: element.hasAttribute('multiple'),
            placeholder: options.placeholder || 'Sélectionner un utilisateur...',
            allowAdd: options.allowAdd !== false,
            addUserUrl: options.addUserUrl || '/api/users',
            searchUrl: options.searchUrl || '/api/users/search',
            ...options
        };

        this.selectedUsers = [];
        this.availableUsers = [];
        this.isOpen = false;

        this.init();
    }

    init() {
        this.createWrapper();
        this.loadExistingUsers();
        this.bindEvents();
        this.element.style.display = 'none';
    }

    createWrapper() {
        const wrapper = document.createElement('div');
        wrapper.className = 'user-select-wrapper relative';

        wrapper.innerHTML = `
            <div class="user-select-input relative">
                <div class="flex flex-wrap items-center min-h-[42px] w-full rounded-lg border border-gray-300 bg-white px-3 py-2 shadow-sm focus-within:border-eco-green-500 focus-within:ring-1 focus-within:ring-eco-green-500 cursor-text">
                    <div class="selected-users flex flex-wrap gap-1 flex-1"></div>
                    <input type="text"
                           class="search-input flex-1 min-w-[120px] border-0 p-0 focus:ring-0 focus:outline-none bg-transparent"
                           placeholder="${this.selectedUsers.length === 0 ? this.options.placeholder : ''}"
                           autocomplete="off">
                    <div class="flex items-center space-x-1 ml-2">
                        ${this.options.allowAdd ? `
                            <button type="button" class="add-user-btn p-1 text-gray-400 hover:text-eco-green-600 transition-colors" title="Ajouter un nouvel utilisateur">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                            </button>
                        ` : ''}
                        <button type="button" class="dropdown-toggle p-1 text-gray-400 hover:text-gray-600 transition-colors">
                            <svg class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="dropdown-menu absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-auto hidden">
                    <div class="user-options"></div>
                    <div class="no-results hidden p-3 text-sm text-gray-500 text-center">
                        Aucun utilisateur trouvé
                    </div>
                </div>
            </div>
        `;

        this.element.parentNode.insertBefore(wrapper, this.element);
        this.wrapper = wrapper;

        // Get references to elements
        this.searchInput = wrapper.querySelector('.search-input');
        this.selectedUsersContainer = wrapper.querySelector('.selected-users');
        this.dropdownMenu = wrapper.querySelector('.dropdown-menu');
        this.userOptions = wrapper.querySelector('.user-options');
        this.noResults = wrapper.querySelector('.no-results');
        this.dropdownToggle = wrapper.querySelector('.dropdown-toggle');
        this.addUserBtn = wrapper.querySelector('.add-user-btn');
    }

    loadExistingUsers() {
        // Load users from the original select options
        const options = this.element.querySelectorAll('option');
        this.availableUsers = Array.from(options).map(option => ({
            id: option.value,
            name: option.textContent,
            email: option.dataset.email || '',
            selected: option.selected
        })).filter(user => user.id); // Remove empty options

        // Set initially selected users
        this.selectedUsers = this.availableUsers.filter(user => user.selected);
        this.updateDisplay();
    }

    bindEvents() {
        // Search input events
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearch(e.target.value);
        });

        this.searchInput.addEventListener('focus', () => {
            this.openDropdown();
        });

        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });

        // Dropdown toggle
        this.dropdownToggle.addEventListener('click', (e) => {
            e.preventDefault();
            this.toggleDropdown();
        });

        // Add user button
        if (this.addUserBtn) {
            this.addUserBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showAddUserModal();
            });
        }

        // Click outside to close
        document.addEventListener('click', (e) => {
            if (!this.wrapper.contains(e.target)) {
                this.closeDropdown();
            }
        });

        // Input container click to focus search
        this.wrapper.querySelector('.user-select-input > div').addEventListener('click', () => {
            this.searchInput.focus();
        });
    }

    handleSearch(query) {
        const filtered = this.availableUsers.filter(user =>
            user.name.toLowerCase().includes(query.toLowerCase()) ||
            user.email.toLowerCase().includes(query.toLowerCase())
        );

        this.renderOptions(filtered);
        this.openDropdown();
    }

    renderOptions(users = this.availableUsers) {
        this.userOptions.innerHTML = '';

        if (users.length === 0) {
            this.noResults.classList.remove('hidden');
            return;
        }

        this.noResults.classList.add('hidden');

        users.forEach(user => {
            const isSelected = this.selectedUsers.some(selected => selected.id === user.id);
            const option = document.createElement('div');
            option.className = `user-option flex items-center justify-between p-3 hover:bg-gray-50 cursor-pointer ${isSelected ? 'bg-eco-green-50 text-eco-green-700' : ''}`;
            option.dataset.userId = user.id;

            option.innerHTML = `
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-eco-green-400 to-eco-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                        ${user.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">${user.name}</div>
                        ${user.email ? `<div class="text-sm text-gray-500">${user.email}</div>` : ''}
                    </div>
                </div>
                ${isSelected ? `
                    <svg class="w-5 h-5 text-eco-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                ` : ''}
            `;

            option.addEventListener('click', () => {
                this.toggleUser(user);
            });

            this.userOptions.appendChild(option);
        });
    }

    toggleUser(user) {
        const index = this.selectedUsers.findIndex(selected => selected.id === user.id);

        if (index > -1) {
            // Remove user
            this.selectedUsers.splice(index, 1);
        } else {
            // Add user
            if (this.options.multiple) {
                this.selectedUsers.push(user);
            } else {
                this.selectedUsers = [user];
                this.closeDropdown();
            }
        }

        this.updateDisplay();
        this.updateOriginalSelect();
        this.renderOptions();

        if (!this.options.multiple) {
            this.searchInput.value = '';
            this.searchInput.placeholder = this.options.placeholder;
        }
    }

    updateDisplay() {
        this.selectedUsersContainer.innerHTML = '';

        this.selectedUsers.forEach(user => {
            const tag = document.createElement('span');
            tag.className = 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-eco-green-100 text-eco-green-800';
            tag.innerHTML = `
                ${user.name}
                <button type="button" class="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-eco-green-200 focus:outline-none" onclick="event.stopPropagation()">
                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            `;

            tag.querySelector('button').addEventListener('click', () => {
                this.toggleUser(user);
            });

            this.selectedUsersContainer.appendChild(tag);
        });

        // Update placeholder
        this.searchInput.placeholder = this.selectedUsers.length === 0 ? this.options.placeholder : '';
    }

    updateOriginalSelect() {
        // Clear all selections
        Array.from(this.element.options).forEach(option => {
            option.selected = false;
        });

        // Set selected options
        this.selectedUsers.forEach(user => {
            const option = this.element.querySelector(`option[value="${user.id}"]`);
            if (option) {
                option.selected = true;
            }
        });

        // Trigger change event
        this.element.dispatchEvent(new Event('change', { bubbles: true }));
    }

    openDropdown() {
        this.isOpen = true;
        this.dropdownMenu.classList.remove('hidden');
        this.dropdownToggle.querySelector('svg').classList.add('rotate-180');
        this.renderOptions();
    }

    closeDropdown() {
        this.isOpen = false;
        this.dropdownMenu.classList.add('hidden');
        this.dropdownToggle.querySelector('svg').classList.remove('rotate-180');
        this.searchInput.value = '';
    }

    toggleDropdown() {
        if (this.isOpen) {
            this.closeDropdown();
        } else {
            this.openDropdown();
        }
    }

    handleKeydown(e) {
        // Handle keyboard navigation
        if (e.key === 'Escape') {
            this.closeDropdown();
        } else if (e.key === 'ArrowDown') {
            e.preventDefault();
            this.openDropdown();
            // Focus first option
        } else if (e.key === 'Enter') {
            e.preventDefault();
            if (!this.isOpen) {
                this.openDropdown();
            }
        }
    }

    showAddUserModal() {
        // Create and show modal for adding new user
        const modal = this.createAddUserModal();
        document.body.appendChild(modal);

        // Focus first input
        setTimeout(() => {
            modal.querySelector('input[name="firstName"]').focus();
        }, 100);
    }

    createAddUserModal() {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 z-50 overflow-y-auto';
        modal.innerHTML = `
            <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <form class="add-user-form">
                        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                            <div class="sm:flex sm:items-start">
                                <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-eco-green-100 sm:mx-0 sm:h-10 sm:w-10">
                                    <svg class="h-6 w-6 text-eco-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                                        Ajouter un nouvel utilisateur
                                    </h3>
                                    <div class="space-y-4">
                                        <div class="grid grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">Prénom</label>
                                                <input type="text" name="firstName" required class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">Nom</label>
                                                <input type="text" name="lastName" required class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500">
                                            </div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                            <input type="email" name="email" required class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                            <button type="submit" class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-eco-green-600 text-base font-medium text-white hover:bg-eco-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-eco-green-500 sm:ml-3 sm:w-auto sm:text-sm">
                                Ajouter
                            </button>
                            <button type="button" class="cancel-btn mt-3 w-full inline-flex justify-center rounded-lg border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                                Annuler
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        // Bind events
        modal.querySelector('.cancel-btn').addEventListener('click', () => {
            modal.remove();
        });

        modal.querySelector('.bg-gray-500').addEventListener('click', () => {
            modal.remove();
        });

        modal.querySelector('.add-user-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleAddUser(modal, new FormData(e.target));
        });

        return modal;
    }

    async handleAddUser(modal, formData) {
        const submitBtn = modal.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        try {
            submitBtn.textContent = 'Ajout en cours...';
            submitBtn.disabled = true;

            // Prepare data for API
            const userData = {
                firstName: formData.get('firstName'),
                lastName: formData.get('lastName'),
                email: formData.get('email')
            };

            // Call API to create user
            const response = await fetch('/api/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(userData)
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Erreur lors de la création de l\'utilisateur');
            }

            const newUser = result.user;

            // Add to available users
            this.availableUsers.push(newUser);

            // Add option to original select
            const option = document.createElement('option');
            option.value = newUser.id;
            option.textContent = newUser.name;
            option.dataset.email = newUser.email;
            this.element.appendChild(option);

            // Select the new user
            this.toggleUser(newUser);

            modal.remove();

            // Show success message with temp password info
            let message = 'Utilisateur ajouté avec succès!';
            if (result.tempPassword) {
                message += ` Mot de passe temporaire: ${result.tempPassword}`;
            }
            this.showNotification(message, 'success');

        } catch (error) {
            console.error('Error adding user:', error);
            this.showNotification(error.message || 'Erreur lors de l\'ajout de l\'utilisateur', 'error');
        } finally {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full max-w-md ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
        }`;

        // Create close button
        const closeBtn = document.createElement('button');
        closeBtn.className = 'absolute top-2 right-2 text-white hover:text-gray-200 focus:outline-none';
        closeBtn.innerHTML = '×';
        closeBtn.style.fontSize = '20px';
        closeBtn.onclick = () => {
            notification.classList.add('translate-x-full');
            setTimeout(() => notification.remove(), 300);
        };

        notification.innerHTML = `<div class="pr-6">${message}</div>`;
        notification.appendChild(closeBtn);

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Remove after longer time if message contains password
        const timeout = message.includes('Mot de passe') ? 10000 : 3000;
        setTimeout(() => {
            if (document.body.contains(notification)) {
                notification.classList.add('translate-x-full');
                setTimeout(() => notification.remove(), 300);
            }
        }, timeout);
    }
}

// Auto-initialize user selects
document.addEventListener('DOMContentLoaded', function() {
    const userSelects = document.querySelectorAll('select[data-user-select]');
    userSelects.forEach(select => {
        new UserSelect(select, {
            allowAdd: select.dataset.allowAdd !== 'false'
        });
    });
});

// Export for manual initialization
window.UserSelect = UserSelect;
