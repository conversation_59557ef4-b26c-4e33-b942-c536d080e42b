#!/bin/bash

echo "🧪 Exécution des tests EcoTask"
echo "=============================="

# Check if we're in the right directory
if [ ! -f "composer.json" ] || [ ! -d "tests" ]; then
    echo "❌ Ce script doit être exécuté depuis la racine du projet EcoTask"
    exit 1
fi

# Install dependencies if needed
if [ ! -d "vendor" ]; then
    echo "📦 Installation des dépendances..."
    composer install --no-interaction --prefer-dist
fi

# Create directories
mkdir -p var/coverage var/reports

echo ""
echo "🧪 Exécution de tous les tests avec couverture..."

# Run PHPUnit with coverage
php bin/phpunit \
    --coverage-html=var/coverage/html \
    --coverage-clover=var/coverage/clover.xml \
    --coverage-text \
    --testdox

echo ""
echo "🔍 Vérification de la couverture..."
if [ -f "scripts/check-coverage.php" ]; then
    php scripts/check-coverage.php
else
    echo "Script de vérification de couverture non trouvé"
fi

echo ""
echo "📊 Analyse statique..."
if [ -f "vendor/bin/phpstan" ]; then
    vendor/bin/phpstan analyse --memory-limit=1G
else
    echo "PHPStan non installé"
fi

echo ""
echo "✅ Tests terminés!"
echo "📊 Rapports disponibles dans var/coverage/"
