<?php

/**
 * Script pour vérifier que la couverture de code atteint le seuil minimum
 */

declare(strict_types=1);

const MINIMUM_COVERAGE = 70.0;
const CLOVER_FILE = __DIR__ . '/../var/coverage/clover.xml';

function checkCoverage(): int
{
    if (!file_exists(CLOVER_FILE)) {
        echo "❌ Fichier de couverture non trouvé: " . CLOVER_FILE . "\n";
        echo "Exécutez d'abord: php bin/phpunit --coverage-clover=var/coverage/clover.xml\n";
        return 1;
    }

    $xml = simplexml_load_file(CLOVER_FILE);
    if ($xml === false) {
        echo "❌ Impossible de lire le fichier de couverture\n";
        return 1;
    }

    $metrics = $xml->project->metrics;
    if (!$metrics) {
        echo "❌ Métriques non trouvées dans le fichier de couverture\n";
        return 1;
    }

    $statements = (int) $metrics['statements'];
    $coveredStatements = (int) $metrics['coveredstatements'];
    
    if ($statements === 0) {
        echo "❌ Aucune instruction trouvée\n";
        return 1;
    }

    $coverage = ($coveredStatements / $statements) * 100;

    echo "📊 Rapport de couverture de code:\n";
    echo "================================\n";
    echo sprintf("Instructions totales: %d\n", $statements);
    echo sprintf("Instructions couvertes: %d\n", $coveredStatements);
    echo sprintf("Couverture: %.2f%%\n", $coverage);
    echo sprintf("Seuil minimum: %.1f%%\n", MINIMUM_COVERAGE);
    echo "\n";

    if ($coverage >= MINIMUM_COVERAGE) {
        echo "✅ Couverture de code suffisante!\n";
        return 0;
    } else {
        echo "❌ Couverture de code insuffisante!\n";
        echo sprintf("Il manque %.2f%% pour atteindre le seuil minimum.\n", MINIMUM_COVERAGE - $coverage);
        return 1;
    }
}

exit(checkCoverage());
