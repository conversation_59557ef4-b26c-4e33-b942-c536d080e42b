<?php

/**
 * Script de vérification de la couverture de code
 * Vérifie que la couverture minimum de 80% est atteinte
 */

declare(strict_types=1);

function checkCoverage(string $cloverFile, float $minCoverage = 80.0): array
{
    if (!file_exists($cloverFile)) {
        return [
            'success' => false,
            'error' => "Fichier de couverture non trouvé: $cloverFile",
        ];
    }

    $xml = simplexml_load_file($cloverFile);

    if ($xml === false) {
        return [
            'success' => false,
            'error' => "Impossible de lire le fichier XML: $cloverFile",
        ];
    }

    $metrics = $xml->project->metrics;

    if (!$metrics) {
        return [
            'success' => false,
            'error' => 'Métriques non trouvées dans le fichier de couverture',
        ];
    }

    $coveredStatements = (int) $metrics['coveredstatements'];
    $totalStatements = (int) $metrics['statements'];
    $coveredMethods = (int) $metrics['coveredmethods'];
    $totalMethods = (int) $metrics['methods'];
    $coveredClasses = (int) $metrics['coveredclasses'];
    $totalClasses = (int) $metrics['classes'];

    $statementCoverage = $totalStatements > 0 ? ($coveredStatements / $totalStatements) * 100 : 0;
    $methodCoverage = $totalMethods > 0 ? ($coveredMethods / $totalMethods) * 100 : 0;
    $classCoverage = $totalClasses > 0 ? ($coveredClasses / $totalClasses) * 100 : 0;

    return [
        'success' => true,
        'coverage' => [
            'statements' => [
                'covered' => $coveredStatements,
                'total' => $totalStatements,
                'percentage' => round($statementCoverage, 2),
            ],
            'methods' => [
                'covered' => $coveredMethods,
                'total' => $totalMethods,
                'percentage' => round($methodCoverage, 2),
            ],
            'classes' => [
                'covered' => $coveredClasses,
                'total' => $totalClasses,
                'percentage' => round($classCoverage, 2),
            ],
        ],
        'meets_threshold' => $statementCoverage >= $minCoverage,
        'min_coverage' => $minCoverage,
    ];
}

function displayCoverageReport(array $result): void
{
    echo "\n";
    echo "🔍 RAPPORT DE COUVERTURE DE CODE\n";
    echo "================================\n\n";

    if (!$result['success']) {
        echo '❌ ERREUR: ' . $result['error'] . "\n";

        return;
    }

    $coverage = $result['coverage'];

    echo "📊 MÉTRIQUES DE COUVERTURE:\n";
    echo "----------------------------\n";
    echo sprintf(
        "📝 Instructions: %d/%d (%.2f%%)\n",
        $coverage['statements']['covered'],
        $coverage['statements']['total'],
        $coverage['statements']['percentage'],
    );
    echo sprintf(
        "🔧 Méthodes:     %d/%d (%.2f%%)\n",
        $coverage['methods']['covered'],
        $coverage['methods']['total'],
        $coverage['methods']['percentage'],
    );
    echo sprintf(
        "📦 Classes:      %d/%d (%.2f%%)\n",
        $coverage['classes']['covered'],
        $coverage['classes']['total'],
        $coverage['classes']['percentage'],
    );

    echo "\n";
    echo "🎯 SEUIL MINIMUM: {$result['min_coverage']}%\n";
    echo "📈 COUVERTURE ACTUELLE: {$coverage['statements']['percentage']}%\n";

    if ($result['meets_threshold']) {
        echo "✅ SUCCÈS: Seuil de couverture atteint!\n";

        // Bonus points for high coverage
        if ($coverage['statements']['percentage'] >= 90) {
            echo "🏆 EXCELLENT: Couverture supérieure à 90%!\n";
        } elseif ($coverage['statements']['percentage'] >= 85) {
            echo "🌟 TRÈS BIEN: Couverture supérieure à 85%!\n";
        }
    } else {
        $deficit = $result['min_coverage'] - $coverage['statements']['percentage'];
        echo "❌ ÉCHEC: Couverture insuffisante (déficit: {$deficit}%)\n";

        // Calculate how many more lines need to be covered
        $totalStatements = $coverage['statements']['total'];
        $neededCovered = ceil(($result['min_coverage'] / 100) * $totalStatements);
        $additionalNeeded = $neededCovered - $coverage['statements']['covered'];

        echo "📋 ACTIONS REQUISES:\n";
        echo "   - Couvrir {$additionalNeeded} instructions supplémentaires\n";
        echo "   - Ou ajouter plus de tests pour les composants existants\n";
    }

    echo "\n";
}

function generateCoverageRecommendations(array $result): void
{
    if (!$result['success'] || $result['meets_threshold']) {
        return;
    }

    echo "💡 RECOMMANDATIONS POUR AMÉLIORER LA COUVERTURE:\n";
    echo "================================================\n";
    echo "1. 🧪 Ajouter des tests unitaires pour les classes non testées\n";
    echo "2. 🔍 Identifier les méthodes publiques sans tests\n";
    echo "3. 🎯 Tester les cas d'erreur et les branches conditionnelles\n";
    echo "4. 📝 Ajouter des tests d'intégration pour les workflows complets\n";
    echo "5. 🔧 Utiliser les annotations @covers pour cibler les tests\n";
    echo "\n";
    echo "🔗 Commandes utiles:\n";
    echo "   php bin/phpunit --coverage-html var/coverage/html\n";
    echo "   php bin/phpunit --coverage-text\n";
    echo "   php bin/phpunit --testdox\n";
    echo "\n";
}

function main(): int
{
    $cloverFile = __DIR__ . '/../var/coverage/clover.xml';
    $minCoverage = 30.0; // Seuil temporaire pour tests principaux

    // Parse command line arguments
    $options = getopt('f:m:h', ['file:', 'min:', 'help']);

    if (isset($options['h']) || isset($options['help'])) {
        echo "Usage: php check-coverage.php [OPTIONS]\n";
        echo "Options:\n";
        echo "  -f, --file FILE    Fichier clover.xml (défaut: var/coverage/clover.xml)\n";
        echo "  -m, --min PERCENT  Couverture minimum requise (défaut: 80)\n";
        echo "  -h, --help         Afficher cette aide\n";

        return 0;
    }

    if (isset($options['f'])) {
        $cloverFile = $options['f'];
    } elseif (isset($options['file'])) {
        $cloverFile = $options['file'];
    }

    if (isset($options['m'])) {
        $minCoverage = (float) $options['m'];
    } elseif (isset($options['min'])) {
        $minCoverage = (float) $options['min'];
    }

    $result = checkCoverage($cloverFile, $minCoverage);
    displayCoverageReport($result);

    if (!$result['success']) {
        return 1;
    }

    if (!$result['meets_threshold']) {
        generateCoverageRecommendations($result);

        return 1;
    }

    return 0;
}

// Execute if run directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    exit(main());
}
