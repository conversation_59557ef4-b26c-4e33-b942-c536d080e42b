#!/bin/bash

# Script de vérification pré-déploiement pour EcoTask
# Vérifie la qualité du code et les tests avant déploiement

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérification de l'environnement
check_environment() {
    log_info "Vérification de l'environnement..."
    
    if [ ! -f "composer.json" ]; then
        log_error "Fichier composer.json non trouvé. Êtes-vous dans le bon répertoire ?"
        exit 1
    fi
    
    if [ ! -f "bin/phpunit" ]; then
        log_error "PHPUnit non trouvé. Exécutez 'composer install' d'abord."
        exit 1
    fi
    
    log_success "Environnement validé"
}

# Installation des dépendances
install_dependencies() {
    log_info "Installation des dépendances de développement pour les tests..."

    # Installer avec les dépendances de dev pour les tests
    composer install --optimize-autoloader --no-scripts

    log_success "Dépendances installées"
}

# Vérification de la syntaxe PHP
check_syntax() {
    log_info "Vérification de la syntaxe PHP..."
    
    find src tests -name "*.php" -exec php -l {} \; > /dev/null
    
    log_success "Syntaxe PHP validée"
}

# Exécution des tests
run_tests() {
    log_info "Exécution des tests..."
    
    # Créer les répertoires nécessaires
    mkdir -p var/coverage
    
    # Exécuter les tests avec couverture
    XDEBUG_MODE=coverage php bin/phpunit \
        tests/Entity/ tests/Service/ tests/Performance/ tests/Controller/ tests/Repository/ \
        --coverage-clover=var/coverage/clover.xml \
        --coverage-html=var/coverage/html \
        --coverage-text=var/coverage/coverage.txt \
        --testdox-text=var/coverage/testdox.txt \
        --stop-on-failure
    
    log_success "Tests exécutés avec succès"
}

# Vérification de la couverture de code
check_coverage() {
    log_info "Vérification de la couverture de code..."
    
    if [ ! -f "scripts/check-coverage.php" ]; then
        log_error "Script de vérification de couverture non trouvé"
        exit 1
    fi
    
    php scripts/check-coverage.php
    
    log_success "Couverture de code validée"
}

# Vérification de sécurité
security_check() {
    log_info "Vérification de sécurité..."
    
    # Audit des dépendances Composer
    composer audit --format=table || AUDIT_FAILED=1
    
    if [ "${AUDIT_FAILED:-0}" -eq 1 ]; then
        log_warning "Des vulnérabilités ont été détectées dans les dépendances"
        log_warning "Consultez le rapport et corrigez avant le déploiement en production"
    else
        log_success "Aucune vulnérabilité détectée"
    fi
}

# Vérification de la configuration
check_configuration() {
    log_info "Vérification de la configuration..."
    
    # Vérifier que les fichiers de configuration existent
    if [ ! -f ".env.production" ]; then
        log_error "Fichier .env.production manquant"
        exit 1
    fi
    
    # Vérifier les variables critiques
    if ! grep -q "APP_SECRET=" .env.production; then
        log_error "APP_SECRET manquant dans .env.production"
        exit 1
    fi
    
    if ! grep -q "DATABASE_URL=" .env.production; then
        log_error "DATABASE_URL manquant dans .env.production"
        exit 1
    fi
    
    log_success "Configuration validée"
}

# Nettoyage des dépendances de développement
cleanup_dev_dependencies() {
    log_info "Nettoyage des dépendances de développement..."

    # Réinstaller sans les dépendances de dev pour la production
    composer install --no-dev --optimize-autoloader --no-scripts

    log_success "Dépendances de développement supprimées"
}

# Génération du rapport de pré-déploiement
generate_report() {
    log_info "Génération du rapport de pré-déploiement..."
    
    REPORT_FILE="var/pre-deploy-report.md"
    mkdir -p var
    
    cat > "$REPORT_FILE" << EOF
# 📋 Rapport de Pré-déploiement EcoTask

**Date**: $(date)
**Commit**: $(git rev-parse HEAD)
**Branche**: $(git branch --show-current)

## ✅ Vérifications Passées

- [x] Syntaxe PHP
- [x] Tests unitaires et fonctionnels
- [x] Couverture de code (≥75%)
- [x] Configuration de production
- [x] Audit de sécurité

## 📊 Statistiques des Tests

EOF

    if [ -f "var/coverage/testdox.txt" ]; then
        echo "### Tests Exécutés" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        head -20 var/coverage/testdox.txt >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
    fi

    if [ -f "var/coverage/coverage.txt" ]; then
        echo "### Couverture de Code" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        tail -10 var/coverage/coverage.txt >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
    fi

    echo "## 🚀 Prêt pour le Déploiement" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    echo "Toutes les vérifications sont passées avec succès." >> "$REPORT_FILE"
    echo "Le code est prêt à être déployé en production." >> "$REPORT_FILE"
    
    log_success "Rapport généré: $REPORT_FILE"
}

# Fonction principale
main() {
    log_info "🔍 Vérifications pré-déploiement EcoTask"
    echo "=================================================="
    
    check_environment
    install_dependencies
    check_syntax
    run_tests
    check_coverage
    security_check
    check_configuration
    cleanup_dev_dependencies
    generate_report
    
    echo ""
    log_success "🎉 Toutes les vérifications sont passées !"
    log_success "✅ Le code est prêt pour le déploiement"
    echo ""
    echo "📋 Rapport disponible: var/pre-deploy-report.md"
    echo "🚀 Vous pouvez maintenant procéder au déploiement"
}

# Gestion des erreurs
trap 'log_error "Vérification pré-déploiement échouée"; exit 1' ERR

# Exécution
main "$@"
