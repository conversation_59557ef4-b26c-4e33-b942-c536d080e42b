#!/bin/bash

# Script pour exécuter tous les tests avec couverture de code
# et vérifier le seuil minimum de 70%

set -e

echo "🧪 EcoTask - Tests avec couverture de code"
echo "=========================================="

# Couleurs pour l'affichage
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Créer les répertoires nécessaires
echo -e "${YELLOW}📁 Création des répertoires...${NC}"
mkdir -p var/coverage .phpunit.cache
chmod -R 755 var .phpunit.cache 2>/dev/null || true

# Nettoyer les anciens rapports
echo -e "${YELLOW}🧹 Nettoyage des anciens rapports...${NC}"
rm -rf var/coverage/*

# Exécuter les tests par catégorie
echo -e "${GREEN}🧪 Exécution des tests des entités...${NC}"
php bin/phpunit tests/Entity/ --no-coverage

echo -e "${GREEN}🧪 Exécution des tests des services...${NC}"
php bin/phpunit tests/Service/Co2CalculatorServiceTest.php --no-coverage

echo -e "${GREEN}🧪 Exécution des tests des repositories...${NC}"
php bin/phpunit tests/Repository/ --no-coverage

# Exécuter tous les tests avec couverture
echo -e "${GREEN}📊 Génération du rapport de couverture...${NC}"
php bin/phpunit tests/Entity tests/Service/Co2CalculatorServiceTest.php tests/Repository/ \
    --coverage-html=var/coverage \
    --coverage-clover=var/coverage/clover.xml \
    --coverage-text

# Vérifier le seuil de couverture
echo -e "${GREEN}🎯 Vérification du seuil de couverture...${NC}"
if php scripts/check-coverage.php; then
    echo -e "${GREEN}✅ Tests réussis avec couverture suffisante !${NC}"
    echo -e "${GREEN}📈 Rapport HTML disponible dans var/coverage/index.html${NC}"
    exit 0
else
    echo -e "${RED}❌ Couverture de code insuffisante !${NC}"
    echo -e "${YELLOW}📈 Consultez le rapport dans var/coverage/index.html${NC}"
    exit 1
fi
