#!/bin/bash

# Script de configuration du monitoring Telegram pour EcoTask
# Usage: ./setup-monitoring.sh

set -e

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonctions utilitaires
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Vérifier les prérequis
check_requirements() {
    log_info "Vérification des prérequis..."
    
    # Vérifier PHP
    if ! command -v php &> /dev/null; then
        log_error "PHP n'est pas installé"
        exit 1
    fi
    
    # Vérifier curl
    if ! command -v curl &> /dev/null; then
        log_error "curl n'est pas installé"
        exit 1
    fi
    
    # Vérifier jq
    if ! command -v jq &> /dev/null; then
        log_warning "jq n'est pas installé (optionnel pour les tests)"
    fi
    
    log_success "Prérequis vérifiés"
}

# Configuration du bot Telegram
setup_telegram() {
    log_info "Configuration du bot Telegram..."
    
    echo "Pour configurer le monitoring Telegram, vous avez besoin de :"
    echo "1. Un bot Telegram (créé via @BotFather)"
    echo "2. L'ID du chat où envoyer les notifications"
    echo ""
    
    # Demander le token du bot
    if [ -z "$TELEGRAM_BOT_TOKEN" ]; then
        echo -n "Token du bot Telegram: "
        read -r TELEGRAM_BOT_TOKEN
    fi
    
    # Demander l'ID du chat
    if [ -z "$TELEGRAM_CHAT_ID" ]; then
        echo -n "ID du chat Telegram: "
        read -r TELEGRAM_CHAT_ID
    fi
    
    # Tester la configuration
    log_info "Test de la configuration Telegram..."
    
    export TELEGRAM_BOT_TOKEN
    export TELEGRAM_CHAT_ID
    
    if php scripts/telegram-monitor.php health; then
        log_success "Configuration Telegram fonctionnelle"
    else
        log_error "Échec du test Telegram"
        exit 1
    fi
}

# Configuration des secrets GitHub
setup_github_secrets() {
    log_info "Configuration des secrets GitHub..."
    
    echo "Ajoutez ces secrets dans votre repository GitHub :"
    echo "Settings > Secrets and variables > Actions > New repository secret"
    echo ""
    echo "TELEGRAM_BOT_TOKEN: $TELEGRAM_BOT_TOKEN"
    echo "TELEGRAM_CHAT_ID: $TELEGRAM_CHAT_ID"
    echo ""
    
    log_warning "N'oubliez pas d'ajouter ces secrets dans GitHub !"
}

# Configuration du cron local (optionnel)
setup_cron() {
    log_info "Configuration du monitoring local (optionnel)..."
    
    echo "Voulez-vous configurer un monitoring local avec cron ? (y/N)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        # Créer le script cron
        cat > /tmp/ecotask-monitor-cron.sh << EOF
#!/bin/bash
cd $(pwd)
export TELEGRAM_BOT_TOKEN="$TELEGRAM_BOT_TOKEN"
export TELEGRAM_CHAT_ID="$TELEGRAM_CHAT_ID"
php scripts/telegram-monitor.php health
EOF
        
        chmod +x /tmp/ecotask-monitor-cron.sh
        
        echo "Script cron créé : /tmp/ecotask-monitor-cron.sh"
        echo "Pour l'installer, ajoutez cette ligne à votre crontab :"
        echo "# Monitoring EcoTask toutes les heures"
        echo "0 * * * * /tmp/ecotask-monitor-cron.sh"
        echo ""
        echo "Commande : crontab -e"
    fi
}

# Test complet du monitoring
test_monitoring() {
    log_info "Test complet du monitoring..."
    
    # Test du health check
    log_info "Test du rapport de santé..."
    if php scripts/telegram-monitor.php health; then
        log_success "Rapport de santé envoyé"
    else
        log_error "Échec du rapport de santé"
    fi
    
    # Test de notification de déploiement
    log_info "Test de notification de déploiement..."
    export GITHUB_REF_NAME="main"
    export GITHUB_SHA="abc123def456"
    export GITHUB_SERVER_URL="https://github.com"
    export GITHUB_REPOSITORY="user/repo"
    export GITHUB_RUN_ID="123456789"
    
    if php scripts/telegram-monitor.php deploy-success; then
        log_success "Notification de déploiement envoyée"
    else
        log_error "Échec de la notification de déploiement"
    fi
    
    # Test d'alerte
    log_info "Test d'alerte critique..."
    if php scripts/telegram-monitor.php alert "Test d'alerte depuis le script de configuration"; then
        log_success "Alerte critique envoyée"
    else
        log_error "Échec de l'alerte critique"
    fi
}

# Afficher l'aide
show_help() {
    echo "Script de configuration du monitoring Telegram pour EcoTask"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Afficher cette aide"
    echo "  -t, --test     Tester le monitoring uniquement"
    echo "  -s, --setup    Configuration complète"
    echo ""
    echo "Variables d'environnement:"
    echo "  TELEGRAM_BOT_TOKEN    Token du bot Telegram"
    echo "  TELEGRAM_CHAT_ID      ID du chat Telegram"
    echo ""
    echo "Exemples:"
    echo "  $0 --setup                    # Configuration complète"
    echo "  $0 --test                     # Test uniquement"
    echo "  TELEGRAM_BOT_TOKEN=xxx TELEGRAM_CHAT_ID=yyy $0 --test"
}

# Fonction principale
main() {
    echo "🤖 Configuration du Monitoring Telegram EcoTask"
    echo "================================================"
    echo ""
    
    case "${1:-setup}" in
        -h|--help)
            show_help
            exit 0
            ;;
        -t|--test)
            check_requirements
            if [ -z "$TELEGRAM_BOT_TOKEN" ] || [ -z "$TELEGRAM_CHAT_ID" ]; then
                log_error "Variables TELEGRAM_BOT_TOKEN et TELEGRAM_CHAT_ID requises"
                exit 1
            fi
            test_monitoring
            ;;
        -s|--setup|setup)
            check_requirements
            setup_telegram
            setup_github_secrets
            setup_cron
            test_monitoring
            ;;
        *)
            log_error "Option inconnue: $1"
            show_help
            exit 1
            ;;
    esac
    
    echo ""
    log_success "Configuration terminée !"
    echo ""
    echo "📋 Prochaines étapes :"
    echo "1. Ajoutez les secrets dans GitHub (si pas encore fait)"
    echo "2. Le monitoring automatique fonctionnera via GitHub Actions"
    echo "3. Vous pouvez tester manuellement avec : php scripts/telegram-monitor.php health"
    echo ""
    echo "📚 Documentation : https://github.com/sami53tk/EcoTask/blob/main/README.md#monitoring"
}

# Exécution
main "$@"
