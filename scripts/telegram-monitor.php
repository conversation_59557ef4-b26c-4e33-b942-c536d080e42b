<?php

/**
 * Script de monitoring intelligent pour Telegram
 * Envoie des notifications contextuelles selon les événements
 */

class TelegramMonitor
{
    private string $botToken;
    private string $chatId;
    private string $baseUrl;

    public function __construct(string $botToken, string $chatId, string $baseUrl = 'https://ecotask.sami-rochdi.fr')
    {
        $this->botToken = $botToken;
        $this->chatId = $chatId;
        $this->baseUrl = rtrim($baseUrl, '/');
    }

    public function sendDeploymentSuccess(array $context = []): void
    {
        $branch = $context['branch'] ?? 'unknown';
        $commit = $context['commit'] ?? 'unknown';
        $environment = $context['environment'] ?? 'production';
        $timestamp = date('Y-m-d H:i:s T');

        $message = "🚀 *Déploiement Réussi*\n\n";
        $message .= "✅ *Statut*: Déployé avec succès\n";
        $message .= "🌐 *Environnement*: " . ucfirst($environment) . "\n";
        $message .= "🔧 *Branche*: `{$branch}`\n";
        $message .= "📝 *Commit*: `" . substr($commit, 0, 8) . "`\n";
        $message .= "⏰ *Timestamp*: {$timestamp}\n";
        $message .= "🌍 *URL*: {$this->baseUrl}\n";

        if (isset($context['workflow_url'])) {
            $message .= "\n🔗 [Voir le workflow]({$context['workflow_url']})";
        }

        $this->sendMessage($message);
    }

    public function sendDeploymentFailure(array $context = []): void
    {
        $branch = $context['branch'] ?? 'unknown';
        $error = $context['error'] ?? 'Erreur inconnue';
        $timestamp = date('Y-m-d H:i:s T');

        $message = "❌ *Déploiement Échoué*\n\n";
        $message .= "🚨 *Statut*: Échec du déploiement\n";
        $message .= "🔧 *Branche*: `{$branch}`\n";
        $message .= "💥 *Erreur*: {$error}\n";
        $message .= "⏰ *Timestamp*: {$timestamp}\n";

        if (isset($context['workflow_url'])) {
            $message .= "\n🔗 [Voir les logs]({$context['workflow_url']})";
        }

        $this->sendMessage($message);
    }

    public function sendHealthReport(): void
    {
        $healthData = $this->checkHealth();
        $timestamp = date('Y-m-d H:i:s T');

        $statusIcon = $healthData['status'] === 'healthy' ? '✅' : '❌';
        $statusText = $healthData['status'] === 'healthy' ? 'Healthy' : 'Unhealthy';

        $message = "📊 *Rapport de Santé EcoTask*\n\n";
        $message .= "{$statusIcon} *Statut Global*: {$statusText}\n";

        // Détails des vérifications
        if (isset($healthData['checks'])) {
            $checks = $healthData['checks'];

            // Base de données
            if (isset($checks['database'])) {
                $dbStatus = $checks['database']['status'] === 'ok' ? '✅' : '❌';
                $message .= "🗄️ *Base de données*: {$dbStatus}\n";
            }

            // Système
            if (isset($checks['system'])) {
                $system = $checks['system'];
                $message .= "💾 *Mémoire*: {$system['memory_usage']}\n";
                $message .= "🐘 *PHP*: {$system['php_version']}\n";
                $message .= "🎵 *Symfony*: {$system['symfony_version']}\n";
            }

            // Uptime
            if (isset($checks['deployment']['uptime'])) {
                $message .= "⏱️ *Uptime*: {$checks['deployment']['uptime']}\n";
            }
        }

        $message .= "\n⏰ *Vérification*: {$timestamp}\n";
        $message .= "🌍 *URL*: {$this->baseUrl}";

        // Ajouter les erreurs si présentes
        if (isset($healthData['errors']) && !empty($healthData['errors'])) {
            $message .= "\n\n⚠️ *Problèmes détectés*:\n";
            foreach ($healthData['errors'] as $error) {
                $message .= "• {$error}\n";
            }
        }

        $this->sendMessage($message);
    }

    public function sendCriticalAlert(string $alert, array $context = []): void
    {
        $timestamp = date('Y-m-d H:i:s T');

        $message = "🚨 *ALERTE CRITIQUE*\n\n";
        $message .= "❌ *Problème*: {$alert}\n";
        $message .= "⏰ *Détecté*: {$timestamp}\n";
        $message .= "🌍 *Site*: {$this->baseUrl}\n";

        if (!empty($context)) {
            $message .= "\n📋 *Détails*:\n";
            foreach ($context as $key => $value) {
                $message .= "• *{$key}*: {$value}\n";
            }
        }

        $message .= "\n🔧 *Action requise*: Vérification immédiate nécessaire";

        $this->sendMessage($message);
    }

    private function checkHealth(): array
    {
        $healthUrl = $this->baseUrl . '/health';

        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'method' => 'GET',
                'header' => 'User-Agent: EcoTask-Monitor/1.0'
            ]
        ]);

        $response = @file_get_contents($healthUrl, false, $context);

        if ($response === false) {
            return [
                'status' => 'unhealthy',
                'errors' => ['Site inaccessible']
            ];
        }

        $data = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'status' => 'unhealthy',
                'errors' => ['Réponse JSON invalide']
            ];
        }

        return $data;
    }

    private function sendMessage(string $message): void
    {
        $url = "https://api.telegram.org/bot{$this->botToken}/sendMessage";

        $data = [
            'chat_id' => $this->chatId,
            'text' => $message,
            'parse_mode' => 'Markdown',
            'disable_web_page_preview' => true
        ];

        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => 'Content-Type: application/x-www-form-urlencoded',
                'content' => http_build_query($data),
                'timeout' => 10
            ]
        ]);

        $result = @file_get_contents($url, false, $context);

        if ($result === false) {
            error_log("Échec de l'envoi de notification Telegram");
        }
    }
}

// Utilisation du script
if (php_sapi_name() === 'cli') {
    $action = $argv[1] ?? 'health';
    $botToken = getenv('TELEGRAM_BOT_TOKEN') ?: '';
    $chatId = getenv('TELEGRAM_CHAT_ID') ?: '';

    if (empty($botToken) || empty($chatId)) {
        echo "❌ Variables d'environnement TELEGRAM_BOT_TOKEN et TELEGRAM_CHAT_ID requises\n";
        exit(1);
    }

    $monitor = new TelegramMonitor($botToken, $chatId);

    switch ($action) {
        case 'health':
            $monitor->sendHealthReport();
            echo "✅ Rapport de santé envoyé\n";
            break;

        case 'deploy-success':
            $context = [
                'branch' => getenv('GITHUB_REF_NAME') ?: 'main',
                'commit' => getenv('GITHUB_SHA') ?: 'unknown',
                'environment' => 'production',
                'workflow_url' => getenv('GITHUB_SERVER_URL') . '/' . getenv('GITHUB_REPOSITORY') . '/actions/runs/' . getenv('GITHUB_RUN_ID')
            ];
            $monitor->sendDeploymentSuccess($context);
            echo "✅ Notification de déploiement réussi envoyée\n";
            break;

        case 'deploy-failure':
            $context = [
                'branch' => getenv('GITHUB_REF_NAME') ?: 'main',
                'error' => $argv[2] ?? 'Erreur de déploiement',
                'workflow_url' => getenv('GITHUB_SERVER_URL') . '/' . getenv('GITHUB_REPOSITORY') . '/actions/runs/' . getenv('GITHUB_RUN_ID')
            ];
            $monitor->sendDeploymentFailure($context);
            echo "✅ Notification d'échec de déploiement envoyée\n";
            break;

        case 'alert':
            $alert = $argv[2] ?? 'Alerte critique';
            $monitor->sendCriticalAlert($alert);
            echo "✅ Alerte critique envoyée\n";
            break;

        default:
            echo "Usage: php telegram-monitor.php [health|deploy-success|deploy-failure|alert] [message]\n";
            exit(1);
    }
}
