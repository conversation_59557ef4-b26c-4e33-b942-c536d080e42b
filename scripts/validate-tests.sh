#!/bin/bash

# Script de validation des tests EcoTask
set -e

echo "🧪 VALIDATION DES TESTS ECOTASK"
echo "==============================="

# Check if we're in the right directory
if [ ! -f "composer.json" ] || [ ! -d "tests" ]; then
    echo "❌ Ce script doit être exécuté depuis la racine du projet EcoTask"
    exit 1
fi

echo "✅ Structure du projet validée"

# Count test files
total_test_files=$(find tests -name "*Test.php" | wc -l)
total_test_methods=$(find tests -name "*Test.php" -exec grep -c "public function test" {} \; 2>/dev/null | awk '{sum+=$1} END {print sum}')

echo "📊 Statistiques:"
echo "   - Fichiers de test: $total_test_files"
echo "   - Méthodes de test: $total_test_methods"

# Check critical test files
echo ""
echo "🔍 Vérification des tests critiques..."

CRITICAL_TESTS=(
    "tests/Entity/TaskTest.php"
    "tests/Entity/ProjectTest.php"
    "tests/Entity/UserTest.php"
    "tests/Controller/HealthControllerTest.php"
    "tests/Repository/TaskRepositoryTest.php"
    "tests/Form/TaskTypeTest.php"
)

for test_file in "${CRITICAL_TESTS[@]}"; do
    if [ -f "$test_file" ]; then
        echo "✅ $test_file"
    else
        echo "❌ $test_file manquant"
    fi
done

echo ""
echo "📋 Configuration PHPUnit..."
if [ -f "phpunit.dist.xml" ]; then
    echo "✅ phpunit.dist.xml configuré"
else
    echo "❌ phpunit.dist.xml manquant"
fi

echo ""
echo "🎯 Prêt pour les tests!"
echo "Exécutez: composer install && php bin/phpunit"
