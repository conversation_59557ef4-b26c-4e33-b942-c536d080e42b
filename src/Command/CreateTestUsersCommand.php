<?php

namespace App\Command;

use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

#[AsCommand(
    name: 'app:create-test-users',
    description: 'Create test users for EcoTask application',
)]
class CreateTestUsersCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private UserPasswordHasherInterface $passwordHasher
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $testUsers = [
            [
                'firstName' => 'Alice',
                'lastName' => 'Martin',
                'email' => '<EMAIL>',
            ],
            [
                'firstName' => 'Bob',
                'lastName' => 'Dupont',
                'email' => '<EMAIL>',
            ],
            [
                'firstName' => 'Claire',
                'lastName' => 'Bernard',
                'email' => '<EMAIL>',
            ],
            [
                'firstName' => 'David',
                'lastName' => 'Moreau',
                'email' => '<EMAIL>',
            ],
            [
                'firstName' => 'Emma',
                'lastName' => 'Petit',
                'email' => '<EMAIL>',
            ],
            [
                'firstName' => 'François',
                'lastName' => 'Roux',
                'email' => '<EMAIL>',
            ],
            [
                'firstName' => 'Gabrielle',
                'lastName' => 'Leroy',
                'email' => '<EMAIL>',
            ],
            [
                'firstName' => 'Hugo',
                'lastName' => 'Simon',
                'email' => '<EMAIL>',
            ],
        ];

        $createdCount = 0;
        $skippedCount = 0;

        foreach ($testUsers as $userData) {
            // Check if user already exists
            $existingUser = $this->entityManager->getRepository(User::class)
                ->findOneBy(['email' => $userData['email']]);

            if ($existingUser) {
                $io->note(sprintf('User %s already exists, skipping...', $userData['email']));
                $skippedCount++;
                continue;
            }

            // Create new user
            $user = new User();
            $user->setFirstName($userData['firstName']);
            $user->setLastName($userData['lastName']);
            $user->setEmail($userData['email']);
            
            // Set a default password (in real app, users would set their own)
            $hashedPassword = $this->passwordHasher->hashPassword($user, 'password123');
            $user->setPassword($hashedPassword);

            $this->entityManager->persist($user);
            $createdCount++;

            $io->success(sprintf('Created user: %s (%s)', $user->getFullName(), $user->getEmail()));
        }

        if ($createdCount > 0) {
            $this->entityManager->flush();
        }

        $io->info(sprintf('Summary: %d users created, %d users skipped', $createdCount, $skippedCount));

        if ($createdCount > 0) {
            $io->note('Default password for all test users is: password123');
            $io->warning('Remember to change passwords in production!');
        }

        return Command::SUCCESS;
    }
}
