<?php

namespace App\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

use const PHP_VERSION;

class HealthController extends AbstractController
{
    #[Route('/health', name: 'app_health', methods: ['GET'])]
    public function health(EntityManagerInterface $entityManager): JsonResponse
    {
        $status = 'healthy';
        $checks = [];
        $errors = [];

        // Vérification de la base de données
        try {
            $connection = $entityManager->getConnection();
            $connection->connect();

            // Test d'une requête simple
            $connection->executeQuery('SELECT 1');
            $checks['database'] = [
                'status' => 'ok',
                'driver' => $connection->getDriver()->getName(),
                'connected' => true
            ];
        } catch (Exception $e) {
            $checks['database'] = [
                'status' => 'error',
                'message' => $e->getMessage(),
                'connected' => false
            ];
            $errors[] = 'Database connection failed';
            $status = 'unhealthy';
        }

        // Vérification des répertoires critiques
        $projectDir = $this->getParameter('kernel.project_dir');
        $directories = [
            'var' => $projectDir . '/var',
            'var/cache' => $projectDir . '/var/cache',
            'var/log' => $projectDir . '/var/log',
        ];

        foreach ($directories as $name => $path) {
            if (is_dir($path) && is_writable($path)) {
                $checks['directories'][$name] = 'ok';
            } else {
                $checks['directories'][$name] = 'error';
                $errors[] = "Directory $name is not writable";
                $status = 'unhealthy';
            }
        }

        // Informations système
        $checks['system'] = [
            'php_version' => PHP_VERSION,
            'symfony_version' => \Symfony\Component\HttpKernel\Kernel::VERSION,
            'environment' => $this->getParameter('kernel.environment'),
            'debug_mode' => $this->getParameter('kernel.debug'),
            'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
        ];

        // Vérification des extensions PHP critiques
        $requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring', 'xml'];
        foreach ($requiredExtensions as $extension) {
            if (extension_loaded($extension)) {
                $checks['php_extensions'][$extension] = 'ok';
            } else {
                $checks['php_extensions'][$extension] = 'missing';
                $errors[] = "PHP extension $extension is missing";
                $status = 'unhealthy';
            }
        }

        // Informations de déploiement
        $checks['deployment'] = [
            'timestamp' => date('c'),
            'uptime' => $this->getUptime(),
            'version' => $this->getAppVersion(),
        ];

        $response = [
            'status' => $status,
            'timestamp' => date('c'),
            'checks' => $checks,
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        return new JsonResponse($response);
    }

    private function getUptime(): string
    {
        if (function_exists('sys_getloadavg')) {
            $uptime = file_get_contents('/proc/uptime');
            if ($uptime !== false) {
                $seconds = (int) explode(' ', $uptime)[0];
                $days = floor($seconds / 86400);
                $hours = floor(($seconds % 86400) / 3600);
                $minutes = floor(($seconds % 3600) / 60);
                return sprintf('%dd %dh %dm', $days, $hours, $minutes);
            }
        }
        return 'unknown';
    }

    private function getAppVersion(): string
    {
        // Essayer de lire la version depuis composer.json
        $composerPath = $this->getParameter('kernel.project_dir') . '/composer.json';
        if (file_exists($composerPath)) {
            $composer = json_decode(file_get_contents($composerPath), true);
            return $composer['version'] ?? 'dev';
        }
        return 'unknown';
    }
}
