<?php

namespace App\Form;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class TaskType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('title', TextType::class, [
                'label' => 'Titre',
                'attr' => ['class' => 'form-control'],
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Description',
                'required' => false,
                'attr' => ['class' => 'form-control', 'rows' => 4],
            ])
            ->add('priority', ChoiceType::class, [
                'label' => 'Priorité',
                'choices' => Task::getPriorities(),
                'attr' => ['class' => 'form-select'],
            ])
            ->add('status', ChoiceType::class, [
                'label' => 'Statut',
                'choices' => Task::getStatuses(),
                'attr' => ['class' => 'form-select'],
            ])
            ->add('type', ChoiceType::class, [
                'label' => 'Type de tâche',
                'choices' => Task::getTypes(),
                'attr' => ['class' => 'form-select'],
                'help' => 'Le type détermine l\'empreinte carbone de la tâche',
            ])
            ->add('dueDate', DateTimeType::class, [
                'label' => 'Date d\'échéance',
                'widget' => 'single_text',
                'required' => false,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('estimatedHours', NumberType::class, [
                'label' => 'Heures estimées',
                'required' => false,
                'attr' => ['class' => 'form-control', 'step' => '0.5', 'min' => '0'],
                'help' => 'Nombre d\'heures estimées pour cette tâche',
            ])
            ->add('actualHours', NumberType::class, [
                'label' => 'Heures réelles',
                'required' => false,
                'attr' => ['class' => 'form-control', 'step' => '0.5', 'min' => '0'],
                'help' => 'Nombre d\'heures réellement passées sur cette tâche',
            ])
            ->add('assignedTo', EntityType::class, [
                'class' => User::class,
                'choice_label' => 'fullName',
                'label' => 'Assigné à',
                'required' => false,
                'placeholder' => 'Sélectionner un utilisateur',
                'attr' => [
                    'class' => 'form-select',
                    'data-user-select' => 'true',
                    'data-allow-add' => 'true'
                ],
                'choice_attr' => function(User $user) {
                    return ['data-email' => $user->getEmail()];
                },
            ])
            ->add('project', EntityType::class, [
                'class' => Project::class,
                'choice_label' => 'name',
                'label' => 'Projet',
                'placeholder' => 'Sélectionner un projet',
                'attr' => ['class' => 'form-select'],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Task::class,
        ]);
    }
}
