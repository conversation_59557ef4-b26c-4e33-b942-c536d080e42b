<?php

namespace App\Service;

use App\Entity\Project;
use App\Entity\Task;

class Co2CalculatorService
{
    /**
     * Calcule les émissions CO2 totales pour un projet
     */
    public function calculateProjectCo2(Project $project): float
    {
        $totalCo2 = 0.0;

        foreach ($project->getTasks() as $task) {
            $totalCo2 += $this->calculateTaskCo2($task);
        }

        return $totalCo2;
    }

    /**
     * Calcule les émissions CO2 pour une tâche
     */
    public function calculateTaskCo2(Task $task): float
    {
        $hours = $task->getActualHours() ?? $task->getEstimatedHours() ?? 0;
        $rate = $this->getCo2RateForTaskType($task->getType());

        return (float) $hours * $rate;
    }

    /**
     * Obtient le taux d'émission CO2 pour un type de tâche
     */
    public function getCo2RateForTaskType(string $taskType): float
    {
        return Task::CO2_RATES[$taskType] ?? Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT];
    }

    /**
     * Calcule les émissions CO2 par type pour un projet
     */
    public function calculateCo2ByType(Project $project): array
    {
        $co2ByType = [
            Task::TYPE_OFFICE_LIGHT => 0.0,
            Task::TYPE_TECHNICAL => 0.0,
            Task::TYPE_ENERGY_INTENSIVE => 0.0,
        ];

        foreach ($project->getTasks() as $task) {
            $taskCo2 = $this->calculateTaskCo2($task);
            $co2ByType[$task->getType()] += $taskCo2;
        }

        return $co2ByType;
    }

    /**
     * Calcule les émissions CO2 moyennes par heure pour un projet
     */
    public function calculateAverageCo2PerHour(Project $project): float
    {
        $totalHours = 0.0;
        $totalCo2 = 0.0;

        foreach ($project->getTasks() as $task) {
            $hours = (float) ($task->getActualHours() ?? $task->getEstimatedHours() ?? 0);
            $co2 = $this->calculateTaskCo2($task);

            $totalHours += $hours;
            $totalCo2 += $co2;
        }

        return $totalHours > 0 ? $totalCo2 / $totalHours : 0.0;
    }

    /**
     * Estime les émissions CO2 futures basées sur les heures estimées
     */
    public function estimateFutureCo2(Project $project): float
    {
        $futureCo2 = 0.0;

        foreach ($project->getTasks() as $task) {
            // Seulement pour les tâches non terminées
            if ($task->getStatus() !== Task::STATUS_DONE) {
                $estimatedHours = (float) ($task->getEstimatedHours() ?? 0);
                $rate = $this->getCo2RateForTaskType($task->getType());
                $futureCo2 += $estimatedHours * $rate;
            }
        }

        return $futureCo2;
    }

    /**
     * Compare les émissions réelles vs estimées
     */
    public function compareActualVsEstimated(Project $project): array
    {
        $actualCo2 = 0.0;
        $estimatedCo2 = 0.0;

        foreach ($project->getTasks() as $task) {
            $actualHours = (float) ($task->getActualHours() ?? 0);
            $estimatedHours = (float) ($task->getEstimatedHours() ?? 0);
            $rate = $this->getCo2RateForTaskType($task->getType());

            $actualCo2 += $actualHours * $rate;
            $estimatedCo2 += $estimatedHours * $rate;
        }

        return [
            'actual' => $actualCo2,
            'estimated' => $estimatedCo2,
            'difference' => $actualCo2 - $estimatedCo2,
            'percentage_difference' => $estimatedCo2 > 0 ? (($actualCo2 - $estimatedCo2) / $estimatedCo2) * 100 : 0,
        ];
    }

    /**
     * Calcule l'impact environnemental équivalent
     */
    public function calculateEnvironmentalImpact(float $co2Kg): array
    {
        // Équivalences approximatives
        return [
            'trees_needed_per_year' => $co2Kg / 22, // Un arbre absorbe ~22kg CO2/an
            'km_car_equivalent' => $co2Kg / 0.12, // ~0.12kg CO2/km pour une voiture moyenne
            'kwh_renewable_equivalent' => $co2Kg / 0.5, // ~0.5kg CO2/kWh évité avec renouvelable
        ];
    }
}
