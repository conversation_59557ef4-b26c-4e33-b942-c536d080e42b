<?php

namespace App\Service;

use App\Entity\Project;
use App\Entity\Task;
use App\Repository\ProjectRepository;
use App\Repository\TaskRepository;
use DateTimeImmutable;

use function count;

class StatisticsService
{
    public function __construct(
        private TaskRepository $taskRepository,
        private ProjectRepository $projectRepository,
        private Co2CalculatorService $co2Calculator,
    ) {
    }

    /**
     * Obtient les statistiques générales du dashboard
     */
    public function getDashboardStatistics(): array
    {
        $totalTasks = $this->taskRepository->count([]);
        $completedTasks = $this->taskRepository->count(['status' => Task::STATUS_DONE]);
        $inProgressTasks = $this->taskRepository->count(['status' => Task::STATUS_IN_PROGRESS]);
        $todoTasks = $this->taskRepository->count(['status' => Task::STATUS_TODO]);

        $completionRate = $totalTasks > 0 ? ($completedTasks / $totalTasks) * 100 : 0;

        return [
            'total_tasks' => $totalTasks,
            'completed_tasks' => $completedTasks,
            'in_progress_tasks' => $inProgressTasks,
            'todo_tasks' => $todoTasks,
            'completion_rate' => round($completionRate, 2),
        ];
    }

    /**
     * Obtient les statistiques CO2 globales
     */
    public function getCo2Statistics(): array
    {
        $allTasks = $this->taskRepository->findAll();
        $totalCo2 = 0;
        $co2ByType = [
            Task::TYPE_OFFICE_LIGHT => 0,
            Task::TYPE_TECHNICAL => 0,
            Task::TYPE_ENERGY_INTENSIVE => 0,
        ];

        foreach ($allTasks as $task) {
            $emission = $this->co2Calculator->calculateTaskCo2($task);
            $totalCo2 += $emission;
            $co2ByType[$task->getType()] += $emission;
        }

        return [
            'total_co2' => $totalCo2,
            'co2_by_type' => $co2ByType,
            'environmental_impact' => $this->co2Calculator->calculateEnvironmentalImpact($totalCo2),
        ];
    }

    /**
     * Obtient les tâches en retard
     */
    public function getOverdueTasks(int $limit = 10): array
    {
        return $this->taskRepository->createQueryBuilder('t')
            ->where('t.dueDate < :now')
            ->andWhere('t.status != :done')
            ->setParameter('now', new DateTimeImmutable())
            ->setParameter('done', Task::STATUS_DONE)
            ->orderBy('t.dueDate', 'ASC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Obtient les tâches récentes
     */
    public function getRecentTasks(int $limit = 10): array
    {
        return $this->taskRepository->findBy([], ['createdAt' => 'DESC'], $limit);
    }

    /**
     * Obtient les statistiques par priorité
     */
    public function getTaskStatsByPriority(): array
    {
        $priorities = [
            Task::PRIORITY_LOW => 0,
            Task::PRIORITY_MEDIUM => 0,
            Task::PRIORITY_HIGH => 0,
            Task::PRIORITY_URGENT => 0,
        ];

        foreach ($priorities as $priority => $count) {
            $priorities[$priority] = $this->taskRepository->count(['priority' => $priority]);
        }

        return $priorities;
    }

    /**
     * Obtient les statistiques par type de tâche
     */
    public function getTaskStatsByType(): array
    {
        $types = [
            Task::TYPE_OFFICE_LIGHT => 0,
            Task::TYPE_TECHNICAL => 0,
            Task::TYPE_ENERGY_INTENSIVE => 0,
        ];

        foreach ($types as $type => $count) {
            $types[$type] = $this->taskRepository->count(['type' => $type]);
        }

        return $types;
    }

    /**
     * Obtient les projets avec leurs statistiques
     */
    public function getProjectsWithStatistics(): array
    {
        $projects = $this->projectRepository->findBy([], ['createdAt' => 'DESC']);
        $projectsWithStats = [];

        foreach ($projects as $project) {
            $projectsWithStats[] = [
                'project' => $project,
                'task_count' => $project->getTasks()->count(),
                'co2_emission' => $this->co2Calculator->calculateProjectCo2($project),
                'member_count' => $project->getMembers()->count(),
                'completion_rate' => $this->calculateProjectCompletionRate($project),
            ];
        }

        // Trier par émissions CO2 décroissantes
        usort($projectsWithStats, static function ($a, $b) {
            return $b['co2_emission'] <=> $a['co2_emission'];
        });

        return $projectsWithStats;
    }

    /**
     * Calcule le taux de completion d'un projet
     */
    public function calculateProjectCompletionRate(Project $project): float
    {
        $tasks = $project->getTasks();
        $totalTasks = $tasks->count();

        if ($totalTasks === 0) {
            return 0.0;
        }

        $completedTasks = 0;

        foreach ($tasks as $task) {
            if ($task->getStatus() === Task::STATUS_DONE) {
                ++$completedTasks;
            }
        }

        return round(($completedTasks / $totalTasks) * 100, 2);
    }

    /**
     * Obtient les tendances temporelles
     */
    public function getTemporalTrends(int $days = 30): array
    {
        $startDate = new DateTimeImmutable("-{$days} days");

        $recentTasks = $this->taskRepository->createQueryBuilder('t')
            ->where('t.createdAt >= :startDate')
            ->setParameter('startDate', $startDate)
            ->getQuery()
            ->getResult();

        $completedTasks = $this->taskRepository->createQueryBuilder('t')
            ->where('t.completedAt >= :startDate')
            ->setParameter('startDate', $startDate)
            ->getQuery()
            ->getResult();

        return [
            'tasks_created' => count($recentTasks),
            'tasks_completed' => count($completedTasks),
            'productivity_rate' => count($recentTasks) > 0 ? (count($completedTasks) / count($recentTasks)) * 100 : 0,
        ];
    }

    /**
     * Obtient les métriques de performance
     */
    public function getPerformanceMetrics(): array
    {
        $allTasks = $this->taskRepository->findAll();
        $totalEstimatedHours = 0;
        $totalActualHours = 0;
        $tasksWithBothHours = 0;

        foreach ($allTasks as $task) {
            $estimated = (float) ($task->getEstimatedHours() ?? 0);
            $actual = (float) ($task->getActualHours() ?? 0);

            if ($estimated > 0 && $actual > 0) {
                $totalEstimatedHours += $estimated;
                $totalActualHours += $actual;
                ++$tasksWithBothHours;
            }
        }

        $estimationAccuracy = $totalEstimatedHours > 0 ?
            (1 - abs($totalActualHours - $totalEstimatedHours) / $totalEstimatedHours) * 100 : 0;

        return [
            'total_estimated_hours' => $totalEstimatedHours,
            'total_actual_hours' => $totalActualHours,
            'estimation_accuracy' => round($estimationAccuracy, 2),
            'average_task_duration' => $tasksWithBothHours > 0 ? $totalActualHours / $tasksWithBothHours : 0,
        ];
    }
}
