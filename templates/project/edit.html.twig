{% extends 'base.html.twig' %}

{% block title %}Modifier le projet - {{ project.name }} - EcoTask{% endblock %}

{% block body %}
<!-- Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8">
    <div class="mb-4 lg:mb-0">
        <div class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
            <a href="{{ path('app_project_index') }}" class="hover:text-eco-green-600 transition-colors duration-200">Projets</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <a href="{{ path('app_project_show', {'id': project.id}) }}" class="hover:text-eco-green-600 transition-colors duration-200">{{ project.name }}</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <span>Modifier</span>
        </div>
        <h1 class="text-3xl font-bold text-gray-900">Modifier le projet</h1>
        <p class="text-gray-600 mt-1">Mettez à jour les informations de votre projet</p>
    </div>
    <div class="flex space-x-3">
        <a href="{{ path('app_project_show', {'id': project.id}) }}" class="inline-flex items-center px-4 py-2 border border-eco-blue-300 text-eco-blue-700 font-medium rounded-lg hover:bg-eco-blue-50 transition-all duration-200">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
            Voir le projet
        </a>
        <a href="{{ path('app_project_index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-all duration-200">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Retour à la liste
        </a>
    </div>
</div>

<!-- Form Card -->
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
        <div class="bg-gradient-to-r from-eco-green-50 to-eco-blue-50 px-6 py-4 border-b border-gray-100">
            <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                <svg class="w-6 h-6 mr-2 text-eco-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Modification de : {{ project.name }}
            </h2>
        </div>
        <div class="p-6">
            {{ form_start(form, {'attr': {'class': 'space-y-6'}}) }}

            <!-- Project Name -->
            <div>
                <label for="{{ form.name.vars.id }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.name.vars.label }}
                    <span class="text-red-500">*</span>
                </label>
                {{ form_widget(form.name, {'attr': {'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500 transition-colors duration-200'}}) }}
                {{ form_errors(form.name) }}
            </div>

            <!-- Project Description -->
            <div>
                <label for="{{ form.description.vars.id }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.description.vars.label }}
                </label>
                {{ form_widget(form.description, {'attr': {'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500 transition-colors duration-200', 'rows': 4}}) }}
                {{ form_errors(form.description) }}
            </div>

            <!-- Project Members -->
            <div>
                <label for="{{ form.members.vars.id }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.members.vars.label }}
                </label>
                {{ form_widget(form.members, {'attr': {'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500 transition-colors duration-200', 'rows': 3}}) }}
                {{ form_errors(form.members) }}
                {% if form.members.vars.help %}
                    <p class="mt-1 text-xs text-gray-500">{{ form.members.vars.help }}</p>
                {% endif %}
            </div>

            <!-- Warning Notice -->
            <div class="bg-gradient-to-br from-amber-50 to-orange-100 rounded-xl p-6 border border-amber-200">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-amber-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-amber-900 mb-2">Attention</h3>
                        <p class="text-amber-700">
                            La modification des membres du projet n'affectera pas les tâches déjà assignées.
                            Vous devrez modifier individuellement les assignations des tâches si nécessaire.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row justify-between space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ path('app_project_show', {'id': project.id}) }}" class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-all duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Annuler
                </a>
                <button type="submit" class="inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r from-eco-green-600 to-eco-green-700 text-white font-semibold rounded-lg hover:from-eco-green-700 hover:to-eco-green-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Sauvegarder les modifications
                </button>
            </div>

            {{ form_end(form) }}
        </div>
    </div>
</div>
{% endblock %}
