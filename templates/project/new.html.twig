{% extends 'base.html.twig' %}

{% block title %}Nouveau projet - EcoTask{% endblock %}

{% block body %}
<!-- Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8">
    <div class="mb-4 lg:mb-0">
        <div class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
            <a href="{{ path('app_project_index') }}" class="hover:text-eco-green-600 transition-colors duration-200">Projets</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <span>Nouveau projet</span>
        </div>
        <h1 class="text-3xl font-bold text-gray-900">Créer un nouveau projet</h1>
        <p class="text-gray-600 mt-1">Organisez vos tâches et suivez l'impact environnemental de votre équipe</p>
    </div>
    <div class="flex space-x-3">
        <a href="{{ path('app_project_index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-all duration-200">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Retour à la liste
        </a>
    </div>
</div>

<!-- Form Card -->
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
        <div class="bg-gradient-to-r from-eco-green-50 to-eco-blue-50 px-6 py-4 border-b border-gray-100">
            <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                <svg class="w-6 h-6 mr-2 text-eco-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Informations du projet
            </h2>
        </div>
        <div class="p-6">
            {{ form_start(form, {'attr': {'class': 'space-y-6'}}) }}

            <!-- Project Name -->
            <div>
                <label for="{{ form.name.vars.id }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.name.vars.label }}
                    <span class="text-red-500">*</span>
                </label>
                {{ form_widget(form.name, {'attr': {'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500 transition-colors duration-200'}}) }}
                {{ form_errors(form.name) }}
            </div>

            <!-- Project Description -->
            <div>
                <label for="{{ form.description.vars.id }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.description.vars.label }}
                </label>
                {{ form_widget(form.description, {'attr': {'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500 transition-colors duration-200', 'rows': 4}}) }}
                {{ form_errors(form.description) }}
            </div>

            <!-- Project Members -->
            <div>
                <label for="{{ form.members.vars.id }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.members.vars.label }}
                </label>
                {{ form_widget(form.members, {'attr': {'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500 transition-colors duration-200', 'rows': 3}}) }}
                {{ form_errors(form.members) }}
                {% if form.members.vars.help %}
                    <p class="mt-1 text-xs text-gray-500">{{ form.members.vars.help }}</p>
                {% endif %}
            </div>

            <!-- EcoTask Info -->
            <div class="bg-gradient-to-br from-eco-blue-50 to-cyan-100 rounded-xl p-6 border border-eco-blue-200">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-eco-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-eco-blue-900 mb-2">À propos des projets EcoTask</h3>
                        <p class="text-eco-blue-700 mb-3">
                            Chaque projet permet de regrouper des tâches et de suivre leur impact environnemental.
                        </p>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-eco-blue-600">
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4 text-eco-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Suivi des émissions CO₂ totales</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4 text-eco-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Analyse de l'efficacité énergétique</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4 text-eco-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Gestion collaborative d'équipe</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4 text-eco-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Rapports environnementaux</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row justify-between space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ path('app_project_index') }}" class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-all duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Annuler
                </a>
                <button type="submit" class="inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r from-eco-green-600 to-eco-green-700 text-white font-semibold rounded-lg hover:from-eco-green-700 hover:to-eco-green-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Créer le projet
                </button>
            </div>

            {{ form_end(form) }}
        </div>
    </div>
</div>
{% endblock %}
