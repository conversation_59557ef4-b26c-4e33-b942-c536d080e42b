<?php

namespace App\Tests\Controller;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class ControllerMethodsTest extends WebTestCase
{
    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        // Ne pas booter le kernel ici
    }

    protected function tearDown(): void
    {
        if (isset($this->entityManager)) {
            $this->entityManager->createQuery('DELETE FROM App\Entity\Task')->execute();
            $this->entityManager->createQuery('DELETE FROM App\Entity\Project')->execute();
            $this->entityManager->createQuery('DELETE FROM App\Entity\User')->execute();
        }

        parent::tearDown();
    }

    public function testTaskControllerShow(): void
    {
        $client = static::createClient();

        // Initialiser l'entity manager
        $kernel = self::bootKernel();
        $this->entityManager = $kernel->getContainer()
            ->get('doctrine')
            ->getManager();

        // Créer une tâche de test
        $task = $this->createTestTask();

        // Tester l'affichage de la tâche
        $client->request('GET', '/task/' . $task->getId());
        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('h1');
    }

    public function testTaskControllerEdit(): void
    {
        $client = static::createClient();

        // Créer une tâche de test
        $task = $this->createTestTask();

        // Tester l'édition de la tâche
        $client->request('GET', '/task/' . $task->getId() . '/edit');
        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('form');
    }

    public function testTaskControllerToggleStatus(): void
    {
        $client = static::createClient();

        // Créer une tâche de test
        $task = $this->createTestTask();

        // Tester le changement de statut
        $client->request('POST', '/task/' . $task->getId() . '/toggle-status');
        $this->assertResponseRedirects();
    }

    public function testProjectControllerShow(): void
    {
        $client = static::createClient();

        // Créer un projet de test
        $project = $this->createTestProject();

        // Tester l'affichage du projet
        $client->request('GET', '/project/' . $project->getId());
        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('h1');
    }

    public function testProjectControllerEdit(): void
    {
        $client = static::createClient();

        // Créer un projet de test
        $project = $this->createTestProject();

        // Tester l'édition du projet
        $client->request('GET', '/project/' . $project->getId() . '/edit');
        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('form');
    }

    public function testTaskFormSubmission(): void
    {
        $client = static::createClient();

        // Créer un projet pour la tâche
        $project = $this->createTestProject();

        // Tester la soumission du formulaire de tâche
        $crawler = $client->request('GET', '/task/new');
        $this->assertResponseIsSuccessful();

        $form = $crawler->selectButton('Créer')->form([
            'task[title]' => 'Test Task Form',
            'task[description]' => 'Test Description',
            'task[priority]' => Task::PRIORITY_MEDIUM,
            'task[status]' => Task::STATUS_TODO,
            'task[type]' => Task::TYPE_OFFICE_LIGHT,
            'task[project]' => $project->getId(),
        ]);

        $client->submit($form);
        $this->assertResponseRedirects('/task/');
    }

    public function testProjectFormSubmission(): void
    {
        $client = static::createClient();

        // Tester la soumission du formulaire de projet
        $crawler = $client->request('GET', '/project/new');
        $this->assertResponseIsSuccessful();

        $form = $crawler->selectButton('Créer')->form([
            'project[name]' => 'Test Project Form',
            'project[description]' => 'Test Project Description',
        ]);

        $client->submit($form);
        $this->assertResponseRedirects('/project/');
    }

    public function testTaskDelete(): void
    {
        $client = static::createClient();

        // Créer une tâche de test
        $task = $this->createTestTask();
        $taskId = $task->getId();

        // Tester la suppression
        $client->request('DELETE', '/task/' . $taskId);
        $this->assertResponseRedirects('/task/');

        // Vérifier que la tâche a été supprimée
        $deletedTask = $this->entityManager->getRepository(Task::class)->find($taskId);
        $this->assertNull($deletedTask);
    }

    public function testProjectDelete(): void
    {
        $client = static::createClient();

        // Créer un projet de test
        $project = $this->createTestProject();
        $projectId = $project->getId();

        // Tester la suppression
        $client->request('DELETE', '/project/' . $projectId);
        $this->assertResponseRedirects('/project/');

        // Vérifier que le projet a été supprimé
        $deletedProject = $this->entityManager->getRepository(Project::class)->find($projectId);
        $this->assertNull($deletedProject);
    }

    public function testDashboardWithData(): void
    {
        $client = static::createClient();

        // Créer des données de test
        $this->createTestTask();
        $this->createTestProject();

        // Tester le dashboard avec des données
        $client->request('GET', '/dashboard');
        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('h1');
    }

    private function createTestTask(): Task
    {
        $project = $this->createTestProject();
        $user = $this->createTestUser();

        $task = new Task();
        $task->setTitle('Test Task');
        $task->setDescription('Test Description');
        $task->setStatus(Task::STATUS_TODO);
        $task->setPriority(Task::PRIORITY_MEDIUM);
        $task->setType(Task::TYPE_OFFICE_LIGHT);
        $task->setProject($project);
        $task->setAssignedTo($user);
        $this->entityManager->persist($task);
        $this->entityManager->flush();

        return $task;
    }

    private function createTestProject(): Project
    {
        $project = new Project();
        $project->setName('Test Project');
        $project->setDescription('Test Description');
        $this->entityManager->persist($project);
        $this->entityManager->flush();

        return $project;
    }

    private function createTestUser(): User
    {
        $user = new User();
        $user->setEmail('test' . uniqid() . '@example.com');
        $user->setFirstName('Test');
        $user->setLastName('User');
        $user->setPassword('password');
        $this->entityManager->persist($user);
        $this->entityManager->flush();

        return $user;
    }
}
