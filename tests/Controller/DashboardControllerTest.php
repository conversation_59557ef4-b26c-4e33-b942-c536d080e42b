<?php

namespace App\Tests\Controller;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class DashboardControllerTest extends WebTestCase
{
    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        // Ne pas booter le kernel ici, le faire dans chaque test si nécessaire
    }

    protected function tearDown(): void
    {
        if (isset($this->entityManager)) {
            // Nettoyer la base de données après chaque test
            $this->entityManager->createQuery('DELETE FROM App\Entity\Task')->execute();
            $this->entityManager->createQuery('DELETE FROM App\Entity\Project')->execute();
            $this->entityManager->createQuery('DELETE FROM App\Entity\User')->execute();
        }

        parent::tearDown();
    }

    public function testDashboardIndex(): void
    {
        $client = static::createClient();
        $client->request('GET', '/dashboard');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('h1', 'Tableau de bord');
    }

    public function testHomeRedirectsToDashboard(): void
    {
        $client = static::createClient();
        $client->request('GET', '/');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('h1', 'Tableau de bord');
    }

    public function testDashboardWithData(): void
    {
        $client = static::createClient();

        // Initialiser l'entity manager
        $kernel = self::bootKernel();
        $this->entityManager = $kernel->getContainer()
            ->get('doctrine')
            ->getManager();

        // Créer des données de test
        $this->createTestData();

        $client->request('GET', '/dashboard');

        $this->assertResponseIsSuccessful();

        // Vérifier que la page se charge correctement
        $this->assertSelectorExists('h1');
    }

    public function testDashboardCo2Statistics(): void
    {
        $client = static::createClient();

        // Initialiser l'entity manager
        $kernel = self::bootKernel();
        $this->entityManager = $kernel->getContainer()
            ->get('doctrine')
            ->getManager();

        // Créer des données de test avec émissions CO2
        $this->createTestDataWithCo2();

        $client->request('GET', '/dashboard');

        $this->assertResponseIsSuccessful();

        // Vérifier que la page se charge correctement
        $this->assertSelectorExists('h1');
    }

    public function testDashboardRecentTasks(): void
    {
        $client = static::createClient();
        $client->request('GET', '/dashboard');

        $this->assertResponseIsSuccessful();

        // Vérifier que la page contient les éléments de base
        $this->assertSelectorExists('h1');
    }

    public function testDashboardOverdueTasks(): void
    {
        $client = static::createClient();
        $client->request('GET', '/dashboard');

        $this->assertResponseIsSuccessful();

        // Vérifier que la page se charge correctement
        $this->assertSelectorExists('body');
    }

    private function createTestData(): void
    {
        $project = new Project();
        $project->setName('Test Project');
        $project->setDescription('Test Description');
        $this->entityManager->persist($project);

        $user = new User();
        $user->setEmail('<EMAIL>');
        $user->setFirstName('John');
        $user->setLastName('Doe');
        $user->setPassword('password');
        $this->entityManager->persist($user);

        // Créer 3 tâches avec différents statuts
        $task1 = new Task();
        $task1->setTitle('Task 1');
        $task1->setStatus(Task::STATUS_TODO);
        $task1->setProject($project);
        $task1->setAssignedTo($user);
        $this->entityManager->persist($task1);

        $task2 = new Task();
        $task2->setTitle('Task 2');
        $task2->setStatus(Task::STATUS_IN_PROGRESS);
        $task2->setProject($project);
        $task2->setAssignedTo($user);
        $this->entityManager->persist($task2);

        $task3 = new Task();
        $task3->setTitle('Task 3');
        $task3->setStatus(Task::STATUS_DONE);
        $task3->setProject($project);
        $task3->setAssignedTo($user);
        $this->entityManager->persist($task3);

        $this->entityManager->flush();
    }

    private function createTestDataWithCo2(): void
    {
        $project = new Project();
        $project->setName('CO2 Test Project');
        $this->entityManager->persist($project);

        $task = new Task();
        $task->setTitle('CO2 Task');
        $task->setType(Task::TYPE_TECHNICAL);
        $task->setActualHours('5.0');
        $task->setProject($project);
        $this->entityManager->persist($task);

        $this->entityManager->flush();
    }

    private function createOverdueTaskData(): void
    {
        $project = new Project();
        $project->setName('Overdue Project');
        $this->entityManager->persist($project);

        $overdueTask = new Task();
        $overdueTask->setTitle('Overdue Task');
        $overdueTask->setStatus(Task::STATUS_TODO);
        $overdueTask->setDueDate(new DateTimeImmutable('-1 day'));
        $overdueTask->setProject($project);
        $this->entityManager->persist($overdueTask);

        $this->entityManager->flush();
    }
}
