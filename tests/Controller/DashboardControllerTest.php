<?php

namespace App\Tests\Controller;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class DashboardControllerTest extends WebTestCase
{
    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        $kernel = self::bootKernel();
        $this->entityManager = $kernel->getContainer()
            ->get('doctrine')
            ->getManager();
    }

    protected function tearDown(): void
    {
        // Nettoyer la base de données après chaque test
        $this->entityManager->createQuery('DELETE FROM App\Entity\Task')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\Project')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\User')->execute();
        
        parent::tearDown();
    }

    public function testDashboardIndex(): void
    {
        $client = static::createClient();
        $client->request('GET', '/dashboard');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('h1', 'Dashboard');
    }

    public function testHomeRedirectsToDashboard(): void
    {
        $client = static::createClient();
        $client->request('GET', '/');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('h1', 'Dashboard');
    }

    public function testDashboardWithData(): void
    {
        // Créer des données de test
        $this->createTestData();

        $client = static::createClient();
        $client->request('GET', '/dashboard');

        $this->assertResponseIsSuccessful();
        
        // Vérifier que les statistiques sont affichées
        $this->assertSelectorExists('.stats-card');
        $this->assertSelectorTextContains('.total-tasks', '3');
        $this->assertSelectorTextContains('.completed-tasks', '1');
        $this->assertSelectorTextContains('.in-progress-tasks', '1');
        $this->assertSelectorTextContains('.todo-tasks', '1');
    }

    public function testDashboardCo2Statistics(): void
    {
        // Créer des données de test avec émissions CO2
        $this->createTestDataWithCo2();

        $client = static::createClient();
        $client->request('GET', '/dashboard');

        $this->assertResponseIsSuccessful();
        
        // Vérifier que les statistiques CO2 sont affichées
        $this->assertSelectorExists('.co2-stats');
        $this->assertSelectorTextContains('.total-co2', 'kg CO2');
    }

    public function testDashboardRecentTasks(): void
    {
        $this->createTestData();

        $client = static::createClient();
        $client->request('GET', '/dashboard');

        $this->assertResponseIsSuccessful();
        
        // Vérifier que les tâches récentes sont affichées
        $this->assertSelectorExists('.recent-tasks');
        $this->assertSelectorTextContains('.recent-tasks', 'Task 1');
    }

    public function testDashboardOverdueTasks(): void
    {
        $this->createOverdueTaskData();

        $client = static::createClient();
        $client->request('GET', '/dashboard');

        $this->assertResponseIsSuccessful();
        
        // Vérifier que les tâches en retard sont affichées
        $this->assertSelectorExists('.overdue-tasks');
    }

    private function createTestData(): void
    {
        $project = new Project();
        $project->setName('Test Project');
        $project->setDescription('Test Description');
        $this->entityManager->persist($project);

        $user = new User();
        $user->setEmail('<EMAIL>');
        $user->setFirstName('John');
        $user->setLastName('Doe');
        $user->setPassword('password');
        $this->entityManager->persist($user);

        // Créer 3 tâches avec différents statuts
        $task1 = new Task();
        $task1->setTitle('Task 1');
        $task1->setStatus(Task::STATUS_TODO);
        $task1->setProject($project);
        $task1->setAssignedTo($user);
        $this->entityManager->persist($task1);

        $task2 = new Task();
        $task2->setTitle('Task 2');
        $task2->setStatus(Task::STATUS_IN_PROGRESS);
        $task2->setProject($project);
        $task2->setAssignedTo($user);
        $this->entityManager->persist($task2);

        $task3 = new Task();
        $task3->setTitle('Task 3');
        $task3->setStatus(Task::STATUS_DONE);
        $task3->setProject($project);
        $task3->setAssignedTo($user);
        $this->entityManager->persist($task3);

        $this->entityManager->flush();
    }

    private function createTestDataWithCo2(): void
    {
        $project = new Project();
        $project->setName('CO2 Test Project');
        $this->entityManager->persist($project);

        $task = new Task();
        $task->setTitle('CO2 Task');
        $task->setType(Task::TYPE_TECHNICAL);
        $task->setActualHours('5.0');
        $task->setProject($project);
        $this->entityManager->persist($task);

        $this->entityManager->flush();
    }

    private function createOverdueTaskData(): void
    {
        $project = new Project();
        $project->setName('Overdue Project');
        $this->entityManager->persist($project);

        $overdueTask = new Task();
        $overdueTask->setTitle('Overdue Task');
        $overdueTask->setStatus(Task::STATUS_TODO);
        $overdueTask->setDueDate(new DateTimeImmutable('-1 day'));
        $overdueTask->setProject($project);
        $this->entityManager->persist($overdueTask);

        $this->entityManager->flush();
    }
}
