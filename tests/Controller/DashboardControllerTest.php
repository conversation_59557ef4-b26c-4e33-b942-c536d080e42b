<?php

namespace App\Tests\Controller;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class DashboardControllerTest extends WebTestCase
{
    public function testDashboardAccess(): void
    {
        $client = static::createClient();
        $client->request('GET', '/');

        // Application allows access without authentication
        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('h1', 'Tableau de bord');
    }

    public function testDashboardContent(): void
    {
        $client = static::createClient();
        $client->request('GET', '/');

        $this->assertResponseIsSuccessful();

        // Check that main elements are present
        $this->assertSelectorExists('.bg-white');
        $this->assertSelectorTextContains('body', 'EcoTask');
        $this->assertSelectorTextContains('body', 'CO₂');
    }

    public function testDashboardResponseTime(): void
    {
        $client = static::createClient();

        $start = microtime(true);
        $client->request('GET', '/');
        $end = microtime(true);

        $responseTime = ($end - $start) * 1000; // en millisecondes

        $this->assertResponseIsSuccessful();
        $this->assertLessThan(2000, $responseTime, 'Dashboard should load in less than 2 seconds');
    }

    public function testDashboardNavigation(): void
    {
        $client = static::createClient();
        $client->request('GET', '/');

        $this->assertResponseIsSuccessful();

        // Check navigation links
        $this->assertSelectorExists('a[href="/task/"]');
        $this->assertSelectorExists('a[href="/project/"]');
        $this->assertSelectorTextContains('nav', 'Tâches');
        $this->assertSelectorTextContains('nav', 'Projets');
    }

    public function testDashboardStatistics(): void
    {
        $client = static::createClient();
        $client->request('GET', '/');

        $this->assertResponseIsSuccessful();

        // Check that statistics sections are present
        $content = $client->getResponse()->getContent();
        $this->assertStringContainsString('Tâches totales', $content);
        $this->assertStringContainsString('Terminées', $content);
        $this->assertStringContainsString('En cours', $content);
        $this->assertStringContainsString('Impact CO₂', $content);
    }
}
