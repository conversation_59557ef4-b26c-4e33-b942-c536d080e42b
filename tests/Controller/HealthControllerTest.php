<?php

namespace App\Tests\Controller;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class HealthControllerTest extends WebTestCase
{
    public function testHealthEndpoint(): void
    {
        $client = static::createClient();
        $client->request('GET', '/health');

        $this->assertResponseIsSuccessful();
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertIsArray($data);
        $this->assertArrayHasKey('status', $data);
        $this->assertArrayHasKey('timestamp', $data);
        $this->assertArrayHasKey('checks', $data);

        // Vérifier la structure des checks
        $checks = $data['checks'];
        $this->assertArrayHasKey('database', $checks);
        $this->assertArrayHasKey('var_writable', $checks);
        $this->assertArrayHasKey('php_version', $checks);
        $this->assertArrayHasKey('symfony_version', $checks);
        $this->assertArrayHasKey('memory_usage', $checks);

        // Vérifier que le statut est soit 'healthy' soit 'unhealthy'
        $this->assertContains($data['status'], ['healthy', 'unhealthy']);

        // Vérifier le format du timestamp
        $this->assertNotEmpty($data['timestamp']);
        $this->assertIsString($data['timestamp']);

        // Vérifier les versions
        $this->assertIsString($checks['php_version']);
        $this->assertIsString($checks['symfony_version']);
        $this->assertStringContainsString('MB', $checks['memory_usage']);
    }

    public function testHealthEndpointOnlyAcceptsGet(): void
    {
        $client = static::createClient();
        
        $client->request('POST', '/health');
        $this->assertResponseStatusCodeSame(Response::HTTP_METHOD_NOT_ALLOWED);

        $client->request('PUT', '/health');
        $this->assertResponseStatusCodeSame(Response::HTTP_METHOD_NOT_ALLOWED);

        $client->request('DELETE', '/health');
        $this->assertResponseStatusCodeSame(Response::HTTP_METHOD_NOT_ALLOWED);
    }

    public function testHealthResponseStructure(): void
    {
        $client = static::createClient();
        $client->request('GET', '/health');

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        // Test que la réponse JSON est valide
        $this->assertNotNull($data);
        $this->assertJson($response->getContent());

        // Test des types de données
        $this->assertIsString($data['status']);
        $this->assertIsString($data['timestamp']);
        $this->assertIsArray($data['checks']);

        // Test que le timestamp est un format ISO 8601 valide
        $timestamp = \DateTime::createFromFormat(\DateTime::ATOM, $data['timestamp']);
        $this->assertInstanceOf(\DateTime::class, $timestamp);
    }
}
