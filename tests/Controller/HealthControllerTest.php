<?php

namespace App\Tests\Controller;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class HealthControllerTest extends WebTestCase
{
    public function testHealthCheck(): void
    {
        $client = static::createClient();
        $client->request('GET', '/health');

        $this->assertResponseIsSuccessful();
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertArrayHasKey('status', $response);
        $this->assertEquals('healthy', $response['status']);
    }

    public function testHealthCheckStructure(): void
    {
        $client = static::createClient();
        $client->request('GET', '/health');

        $response = json_decode($client->getResponse()->getContent(), true);

        $this->assertArrayHasKey('status', $response);
        $this->assertArrayHasKey('timestamp', $response);
        $this->assertArrayHasKey('checks', $response);
    }

    public function testHealthCheckResponseTime(): void
    {
        $client = static::createClient();

        $start = microtime(true);
        $client->request('GET', '/health');
        $end = microtime(true);

        $responseTime = ($end - $start) * 1000; // en millisecondes

        $this->assertResponseIsSuccessful();
        $this->assertLessThan(100, $responseTime, 'Health check should respond in less than 100ms');
    }

    public function testHealthCheckContent(): void
    {
        $client = static::createClient();
        $client->request('GET', '/health');

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertArrayHasKey('checks', $response);
        $this->assertArrayHasKey('database', $response['checks']);
        $this->assertArrayHasKey('php_version', $response['checks']);
    }

    public function testHealthCheckMethod(): void
    {
        $client = static::createClient();

        // Test GET method
        $client->request('GET', '/health');
        $this->assertResponseIsSuccessful();

        // Test POST method should not be allowed
        $client->request('POST', '/health');
        $this->assertResponseStatusCodeSame(405);
    }
}
