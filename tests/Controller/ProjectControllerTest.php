<?php

namespace App\Tests\Controller;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class ProjectControllerTest extends WebTestCase
{
    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        $kernel = self::bootKernel();
        $this->entityManager = $kernel->getContainer()
            ->get('doctrine')
            ->getManager();
    }

    protected function tearDown(): void
    {
        $this->entityManager->createQuery('DELETE FROM App\Entity\Task')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\Project')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\User')->execute();
        
        parent::tearDown();
    }

    public function testProjectIndex(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('h1', 'Projets');
    }

    public function testProjectIndexWithProjects(): void
    {
        $this->createTestProject();

        $client = static::createClient();
        $client->request('GET', '/project/');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('.project-list', 'Test Project');
    }

    public function testProjectNew(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/new');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('form[name="project"]');
        $this->assertSelectorExists('input[name="project[name]"]');
        $this->assertSelectorExists('textarea[name="project[description]"]');
    }

    public function testProjectCreate(): void
    {
        $client = static::createClient();
        $crawler = $client->request('GET', '/project/new');

        $form = $crawler->selectButton('Créer')->form([
            'project[name]' => 'New Test Project',
            'project[description]' => 'Test Project Description',
        ]);

        $client->submit($form);

        $this->assertResponseRedirects('/project/');
        
        // Vérifier que le projet a été créé
        $project = $this->entityManager->getRepository(Project::class)->findOneBy(['name' => 'New Test Project']);
        $this->assertNotNull($project);
        $this->assertEquals('Test Project Description', $project->getDescription());
    }

    public function testProjectShow(): void
    {
        $project = $this->createTestProjectWithTasks();

        $client = static::createClient();
        $client->request('GET', '/project/' . $project->getId());

        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('h1', 'Test Project');
        $this->assertSelectorTextContains('.project-details', 'Test Description');
        $this->assertSelectorExists('.co2-stats');
        $this->assertSelectorExists('.task-list');
    }

    public function testProjectEdit(): void
    {
        $project = $this->createTestProject();

        $client = static::createClient();
        $client->request('GET', '/project/' . $project->getId() . '/edit');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('form[name="project"]');
        $this->assertInputValueSame('project[name]', 'Test Project');
    }

    public function testProjectUpdate(): void
    {
        $project = $this->createTestProject();

        $client = static::createClient();
        $crawler = $client->request('GET', '/project/' . $project->getId() . '/edit');

        $form = $crawler->selectButton('Modifier')->form([
            'project[name]' => 'Updated Project Name',
            'project[description]' => 'Updated Description',
        ]);

        $client->submit($form);

        $this->assertResponseRedirects('/project/');
        
        // Vérifier que le projet a été mis à jour
        $this->entityManager->refresh($project);
        $this->assertEquals('Updated Project Name', $project->getName());
        $this->assertEquals('Updated Description', $project->getDescription());
        $this->assertNotNull($project->getUpdatedAt());
    }

    public function testProjectDelete(): void
    {
        $project = $this->createTestProject();
        $projectId = $project->getId();

        $client = static::createClient();
        $crawler = $client->request('GET', '/project/' . $projectId);

        $form = $crawler->selectButton('Supprimer')->form();
        $client->submit($form);

        $this->assertResponseRedirects('/project/');
        
        // Vérifier que le projet a été supprimé
        $deletedProject = $this->entityManager->getRepository(Project::class)->find($projectId);
        $this->assertNull($deletedProject);
    }

    public function testProjectShowWithCo2Statistics(): void
    {
        $project = $this->createTestProjectWithTasks();

        $client = static::createClient();
        $client->request('GET', '/project/' . $project->getId());

        $this->assertResponseIsSuccessful();
        
        // Vérifier que les statistiques CO2 sont affichées
        $this->assertSelectorExists('.co2-stats');
        $this->assertSelectorTextContains('.total-co2', 'kg CO2');
        $this->assertSelectorExists('.co2-by-type');
    }

    public function testProjectNotFound(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/999999');

        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    public function testProjectIndexShowsStatistics(): void
    {
        $this->createTestProjectWithTasks();

        $client = static::createClient();
        $client->request('GET', '/project/');

        $this->assertResponseIsSuccessful();
        
        // Vérifier que les statistiques sont affichées dans la liste
        $this->assertSelectorExists('.project-stats');
        $this->assertSelectorTextContains('.task-count', '2');
        $this->assertSelectorExists('.co2-emission');
    }

    private function createTestProject(): Project
    {
        $project = new Project();
        $project->setName('Test Project');
        $project->setDescription('Test Description');
        $this->entityManager->persist($project);
        $this->entityManager->flush();

        return $project;
    }

    private function createTestProjectWithTasks(): Project
    {
        $project = $this->createTestProject();

        $user = new User();
        $user->setEmail('<EMAIL>');
        $user->setFirstName('John');
        $user->setLastName('Doe');
        $user->setPassword('password');
        $this->entityManager->persist($user);

        // Créer des tâches avec différents types pour tester les calculs CO2
        $task1 = new Task();
        $task1->setTitle('Task 1');
        $task1->setType(Task::TYPE_OFFICE_LIGHT);
        $task1->setActualHours('2.0');
        $task1->setProject($project);
        $task1->setAssignedTo($user);
        $this->entityManager->persist($task1);

        $task2 = new Task();
        $task2->setTitle('Task 2');
        $task2->setType(Task::TYPE_TECHNICAL);
        $task2->setActualHours('4.0');
        $task2->setProject($project);
        $task2->setAssignedTo($user);
        $this->entityManager->persist($task2);

        $this->entityManager->flush();

        return $project;
    }
}
