<?php

namespace App\Tests\Controller;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class ProjectControllerTest extends WebTestCase
{
    public function testProjectIndexAccess(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('h1', 'Gestion des projets');
    }

    public function testProjectIndexContent(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/');

        $this->assertResponseIsSuccessful();

        // Check that main elements are present
        $this->assertSelectorExists('body');
        $this->assertSelectorTextContains('body', 'Projets');
    }

    public function testProjectNewAccess(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/new');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('form');
    }

    public function testProjectNewForm(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/new');

        $this->assertResponseIsSuccessful();

        // Check that form elements are present
        $this->assertSelectorExists('input[name="project[name]"]');
        $this->assertSelectorExists('textarea[name="project[description]"]');
    }

    public function testProjectResponseTime(): void
    {
        $client = static::createClient();

        $start = microtime(true);
        $client->request('GET', '/project/');
        $end = microtime(true);

        $responseTime = ($end - $start) * 1000; // en millisecondes

        $this->assertResponseIsSuccessful();
        $this->assertLessThan(2000, $responseTime, 'Project index should load in less than 2 seconds');
    }

    public function testProjectNavigation(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/');

        $this->assertResponseIsSuccessful();

        // Check navigation elements
        $this->assertSelectorExists('a[href="/project/new"]');
        $this->assertSelectorTextContains('body', 'Nouveau projet');
    }

    public function testProjectEmptyState(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/');

        $this->assertResponseIsSuccessful();

        // Check empty state handling
        $content = $client->getResponse()->getContent();
        $this->assertStringContainsString('projet', $content);
    }

    public function testProjectFormValidation(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/new');

        $this->assertResponseIsSuccessful();

        // Check that required fields are marked
        $content = $client->getResponse()->getContent();
        $this->assertStringContainsString('required', $content);
    }

    public function testProjectCO2Information(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/');

        $this->assertResponseIsSuccessful();

        // Check that environmental information is available
        $content = $client->getResponse()->getContent();
        $this->assertStringContainsString('environnemental', $content);
    }

    public function testProjectSearch(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/');

        $this->assertResponseIsSuccessful();

        // Check that project management functionality is available
        $content = $client->getResponse()->getContent();
        $this->assertStringContainsString('projet', $content);
    }
}
