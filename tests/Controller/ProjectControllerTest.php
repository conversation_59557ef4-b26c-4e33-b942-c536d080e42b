<?php

namespace App\Tests\Controller;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class ProjectControllerTest extends WebTestCase
{
    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        // Ne pas booter le kernel ici
    }

    protected function tearDown(): void
    {
        if (isset($this->entityManager)) {
            $this->entityManager->createQuery('DELETE FROM App\Entity\Task')->execute();
            $this->entityManager->createQuery('DELETE FROM App\Entity\Project')->execute();
            $this->entityManager->createQuery('DELETE FROM App\Entity\User')->execute();
        }

        parent::tearDown();
    }

    public function testProjectIndex(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('h1');
    }

    public function testProjectIndexWithProjects(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('body');
    }

    public function testProjectNew(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/new');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('form');
    }

    public function testProjectCreate(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/new');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('form');
    }

    public function testProjectNotFound(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/999999');

        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }
}
