<?php

namespace App\Tests\Controller;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class SimpleControllerTest extends WebTestCase
{
    public function testAllMainRoutes(): void
    {
        $client = static::createClient();

        $routes = [
            '/' => 200,
            '/dashboard' => 200,
            '/task/' => 200,
            '/task/new' => 200,
            '/project/' => 200,
            '/project/new' => 200,
            '/health' => 200,
        ];

        foreach ($routes as $route => $expectedStatus) {
            $client->request('GET', $route);
            $this->assertEquals(
                $expectedStatus,
                $client->getResponse()->getStatusCode(),
                "Route {$route} should return status {$expectedStatus}",
            );
        }
    }

    public function testTaskRouteWithInvalidId(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/99999');
        $this->assertEquals(404, $client->getResponse()->getStatusCode());
    }

    public function testProjectRouteWithInvalidId(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/99999');
        $this->assertEquals(404, $client->getResponse()->getStatusCode());
    }

    public function testTaskEditWithInvalidId(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/99999/edit');
        $this->assertEquals(404, $client->getResponse()->getStatusCode());
    }

    public function testProjectEditWithInvalidId(): void
    {
        $client = static::createClient();
        $client->request('GET', '/project/99999/edit');
        $this->assertEquals(404, $client->getResponse()->getStatusCode());
    }

    public function testHealthEndpointFormat(): void
    {
        $client = static::createClient();
        $client->request('GET', '/health');

        $this->assertResponseIsSuccessful();
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertIsArray($data);
        $this->assertArrayHasKey('status', $data);
        $this->assertArrayHasKey('timestamp', $data);
        $this->assertArrayHasKey('checks', $data);
    }

    public function testFormPages(): void
    {
        $client = static::createClient();

        // Test task form
        $client->request('GET', '/task/new');
        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('form');

        // Test project form
        $client->request('GET', '/project/new');
        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('form');
    }

    public function testPageTitles(): void
    {
        $client = static::createClient();

        $client->request('GET', '/dashboard');
        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('h1');

        $client->request('GET', '/task/');
        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('h1');

        $client->request('GET', '/project/');
        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('h1');
    }

    public function testResponseHeaders(): void
    {
        $client = static::createClient();

        $client->request('GET', '/dashboard');
        $this->assertResponseIsSuccessful();
        $this->assertResponseHeaderSame('content-type', 'text/html; charset=UTF-8');

        $client->request('GET', '/health');
        $this->assertResponseIsSuccessful();
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testMethodNotAllowed(): void
    {
        $client = static::createClient();

        // Test POST sur health (devrait être GET seulement)
        $client->request('POST', '/health');
        $this->assertEquals(405, $client->getResponse()->getStatusCode());
    }

    public function testBasicSecurity(): void
    {
        $client = static::createClient();

        // Test que les pages principales sont accessibles
        $client->request('GET', '/dashboard');
        $this->assertResponseIsSuccessful();

        // Test que les formulaires ont des tokens CSRF
        $crawler = $client->request('GET', '/task/new');
        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('input[name*="token"]');
    }
}
