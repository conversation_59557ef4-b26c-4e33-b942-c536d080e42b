<?php

namespace App\Tests\Controller;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class TaskControllerTest extends WebTestCase
{
    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        $kernel = self::bootKernel();
        $this->entityManager = $kernel->getContainer()
            ->get('doctrine')
            ->getManager();
    }

    protected function tearDown(): void
    {
        $this->entityManager->createQuery('DELETE FROM App\Entity\Task')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\Project')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\User')->execute();
        
        parent::tearDown();
    }

    public function testTaskIndex(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('h1', 'Tâches');
    }

    public function testTaskIndexWithTasks(): void
    {
        $this->createTestTask();

        $client = static::createClient();
        $client->request('GET', '/task/');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('.task-list', 'Test Task');
    }

    public function testTaskNew(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/new');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('form[name="task"]');
        $this->assertSelectorExists('input[name="task[title]"]');
        $this->assertSelectorExists('select[name="task[priority]"]');
        $this->assertSelectorExists('select[name="task[status]"]');
        $this->assertSelectorExists('select[name="task[type]"]');
    }

    public function testTaskCreate(): void
    {
        $client = static::createClient();
        $crawler = $client->request('GET', '/task/new');

        $form = $crawler->selectButton('Créer')->form([
            'task[title]' => 'New Test Task',
            'task[description]' => 'Test Description',
            'task[priority]' => Task::PRIORITY_HIGH,
            'task[status]' => Task::STATUS_TODO,
            'task[type]' => Task::TYPE_TECHNICAL,
            'task[estimatedHours]' => '5.0',
        ]);

        $client->submit($form);

        $this->assertResponseRedirects('/task/');
        
        // Vérifier que la tâche a été créée
        $task = $this->entityManager->getRepository(Task::class)->findOneBy(['title' => 'New Test Task']);
        $this->assertNotNull($task);
        $this->assertEquals('Test Description', $task->getDescription());
        $this->assertEquals(Task::PRIORITY_HIGH, $task->getPriority());
    }

    public function testTaskShow(): void
    {
        $task = $this->createTestTask();

        $client = static::createClient();
        $client->request('GET', '/task/' . $task->getId());

        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('h1', 'Test Task');
        $this->assertSelectorTextContains('.task-details', 'Test Description');
    }

    public function testTaskEdit(): void
    {
        $task = $this->createTestTask();

        $client = static::createClient();
        $client->request('GET', '/task/' . $task->getId() . '/edit');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('form[name="task"]');
        $this->assertInputValueSame('task[title]', 'Test Task');
    }

    public function testTaskUpdate(): void
    {
        $task = $this->createTestTask();

        $client = static::createClient();
        $crawler = $client->request('GET', '/task/' . $task->getId() . '/edit');

        $form = $crawler->selectButton('Modifier')->form([
            'task[title]' => 'Updated Task Title',
            'task[description]' => 'Updated Description',
        ]);

        $client->submit($form);

        $this->assertResponseRedirects('/task/');
        
        // Vérifier que la tâche a été mise à jour
        $this->entityManager->refresh($task);
        $this->assertEquals('Updated Task Title', $task->getTitle());
        $this->assertEquals('Updated Description', $task->getDescription());
        $this->assertNotNull($task->getUpdatedAt());
    }

    public function testTaskDelete(): void
    {
        $task = $this->createTestTask();
        $taskId = $task->getId();

        $client = static::createClient();
        $crawler = $client->request('GET', '/task/' . $taskId);

        $form = $crawler->selectButton('Supprimer')->form();
        $client->submit($form);

        $this->assertResponseRedirects('/task/');
        
        // Vérifier que la tâche a été supprimée
        $deletedTask = $this->entityManager->getRepository(Task::class)->find($taskId);
        $this->assertNull($deletedTask);
    }

    public function testTaskToggleStatus(): void
    {
        $task = $this->createTestTask();
        $this->assertEquals(Task::STATUS_TODO, $task->getStatus());

        $client = static::createClient();
        $crawler = $client->request('GET', '/task/' . $task->getId());

        // Trouver et soumettre le formulaire de changement de statut
        $form = $crawler->filter('form[action*="toggle-status"]')->form();
        $client->submit($form);

        $this->assertResponseRedirects();
        
        // Vérifier que le statut a changé
        $this->entityManager->refresh($task);
        $this->assertEquals(Task::STATUS_IN_PROGRESS, $task->getStatus());
    }

    public function testTaskToggleStatusCycle(): void
    {
        $task = $this->createTestTask();

        $client = static::createClient();
        
        // TODO -> IN_PROGRESS
        $crawler = $client->request('GET', '/task/' . $task->getId());
        $form = $crawler->filter('form[action*="toggle-status"]')->form();
        $client->submit($form);
        $this->entityManager->refresh($task);
        $this->assertEquals(Task::STATUS_IN_PROGRESS, $task->getStatus());

        // IN_PROGRESS -> DONE
        $crawler = $client->request('GET', '/task/' . $task->getId());
        $form = $crawler->filter('form[action*="toggle-status"]')->form();
        $client->submit($form);
        $this->entityManager->refresh($task);
        $this->assertEquals(Task::STATUS_DONE, $task->getStatus());

        // DONE -> TODO
        $crawler = $client->request('GET', '/task/' . $task->getId());
        $form = $crawler->filter('form[action*="toggle-status"]')->form();
        $client->submit($form);
        $this->entityManager->refresh($task);
        $this->assertEquals(Task::STATUS_TODO, $task->getStatus());
        $this->assertNull($task->getCompletedAt());
    }

    public function testTaskNotFound(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/999999');

        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    private function createTestTask(): Task
    {
        $project = new Project();
        $project->setName('Test Project');
        $this->entityManager->persist($project);

        $user = new User();
        $user->setEmail('<EMAIL>');
        $user->setFirstName('John');
        $user->setLastName('Doe');
        $user->setPassword('password');
        $this->entityManager->persist($user);

        $task = new Task();
        $task->setTitle('Test Task');
        $task->setDescription('Test Description');
        $task->setStatus(Task::STATUS_TODO);
        $task->setPriority(Task::PRIORITY_MEDIUM);
        $task->setType(Task::TYPE_OFFICE_LIGHT);
        $task->setProject($project);
        $task->setAssignedTo($user);
        $this->entityManager->persist($task);

        $this->entityManager->flush();

        return $task;
    }
}
