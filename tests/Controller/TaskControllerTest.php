<?php

namespace App\Tests\Controller;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class TaskControllerTest extends WebTestCase
{
    public function testTaskIndexAccess(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('h1', 'Gestion des tâches');
    }

    public function testTaskIndexContent(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/');

        $this->assertResponseIsSuccessful();

        // Check that main elements are present
        $this->assertSelectorExists('body');
        $this->assertSelectorTextContains('body', 'Tâches');
    }

    public function testTaskNewAccess(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/new');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('form');
    }

    public function testTaskNewForm(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/new');

        $this->assertResponseIsSuccessful();

        // Check that form elements are present
        $this->assertSelectorExists('input[name="task[title]"]');
        $this->assertSelectorExists('textarea[name="task[description]"]');
        $this->assertSelectorExists('select[name="task[priority]"]');
        $this->assertSelectorExists('select[name="task[type]"]');
    }

    public function testTaskResponseTime(): void
    {
        $client = static::createClient();

        $start = microtime(true);
        $client->request('GET', '/task/');
        $end = microtime(true);

        $responseTime = ($end - $start) * 1000; // en millisecondes

        $this->assertResponseIsSuccessful();
        $this->assertLessThan(2000, $responseTime, 'Task index should load in less than 2 seconds');
    }

    public function testTaskNavigation(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/');

        $this->assertResponseIsSuccessful();

        // Check navigation elements
        $this->assertSelectorExists('a[href="/task/new"]');
        $this->assertSelectorTextContains('body', 'Nouvelle tâche');
    }

    public function testTaskFilters(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/');

        $this->assertResponseIsSuccessful();

        // Check that task management elements are present
        $content = $client->getResponse()->getContent();
        $this->assertStringContainsString('tâche', $content);
    }

    public function testTaskStatusOptions(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/new');

        $this->assertResponseIsSuccessful();

        // Check that status options are available
        $content = $client->getResponse()->getContent();
        $this->assertStringContainsString('À faire', $content);
        $this->assertStringContainsString('En cours', $content);
        $this->assertStringContainsString('Terminé', $content);
    }

    public function testTaskPriorityOptions(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/new');

        $this->assertResponseIsSuccessful();

        // Check that priority options are available
        $content = $client->getResponse()->getContent();
        $this->assertStringContainsString('Faible', $content);
        $this->assertStringContainsString('Moyenne', $content);
        $this->assertStringContainsString('Haute', $content);
        $this->assertStringContainsString('Urgente', $content);
    }

    public function testTaskTypeOptions(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/new');

        $this->assertResponseIsSuccessful();

        // Check that type options are available
        $content = $client->getResponse()->getContent();
        $this->assertStringContainsString('Bureautique légère', $content);
        $this->assertStringContainsString('Tâche technique', $content);
        $this->assertStringContainsString('Forte intensité', $content);
    }
}
