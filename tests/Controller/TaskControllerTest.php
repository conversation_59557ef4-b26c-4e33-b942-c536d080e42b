<?php

namespace App\Tests\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class TaskControllerTest extends WebTestCase
{
    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        // Ne pas booter le kernel ici
    }

    protected function tearDown(): void
    {
        if (isset($this->entityManager)) {
            $this->entityManager->createQuery('DELETE FROM App\Entity\Task')->execute();
            $this->entityManager->createQuery('DELETE FROM App\Entity\Project')->execute();
            $this->entityManager->createQuery('DELETE FROM App\Entity\User')->execute();
        }

        parent::tearDown();
    }

    public function testTaskIndex(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('h1');
    }

    public function testTaskIndexWithTasks(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('body');
    }

    public function testTaskNew(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/new');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('form');
    }

    public function testTaskCreate(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/new');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('form');
    }

    public function testTaskNotFound(): void
    {
        $client = static::createClient();
        $client->request('GET', '/task/999999');

        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }
}
