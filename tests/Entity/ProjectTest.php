<?php

namespace App\Tests\Entity;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use DateTimeImmutable;
use PHPUnit\Framework\TestCase;

class ProjectTest extends TestCase
{
    private Project $project;

    protected function setUp(): void
    {
        $this->project = new Project();
    }

    public function testProjectCreation(): void
    {
        $this->assertNull($this->project->getId());
        $this->assertNull($this->project->getName());
        $this->assertNull($this->project->getDescription());
        $this->assertNull($this->project->getUpdatedAt());
        $this->assertInstanceOf(DateTimeImmutable::class, $this->project->getCreatedAt());
        $this->assertCount(0, $this->project->getTasks());
        $this->assertCount(0, $this->project->getMembers());
    }

    public function testSettersAndGetters(): void
    {
        $name = 'Test Project';
        $description = 'This is a test project';
        $createdAt = new DateTimeImmutable('2023-01-01');
        $updatedAt = new DateTimeImmutable('2023-01-02');

        $this->project->setName($name);
        $this->project->setDescription($description);
        $this->project->setCreatedAt($createdAt);
        $this->project->setUpdatedAt($updatedAt);

        $this->assertEquals($name, $this->project->getName());
        $this->assertEquals($description, $this->project->getDescription());
        $this->assertEquals($createdAt, $this->project->getCreatedAt());
        $this->assertEquals($updatedAt, $this->project->getUpdatedAt());
    }

    public function testTaskManagement(): void
    {
        $task1 = new Task();
        $task1->setTitle('Task 1');
        $task1->setType(Task::TYPE_OFFICE_LIGHT);
        $task1->setActualHours('2.0');
        
        $task2 = new Task();
        $task2->setTitle('Task 2');
        $task2->setType(Task::TYPE_TECHNICAL);
        $task2->setActualHours('4.0');

        // Test adding tasks
        $this->project->addTask($task1);
        $this->project->addTask($task2);

        $this->assertCount(2, $this->project->getTasks());
        $this->assertTrue($this->project->getTasks()->contains($task1));
        $this->assertTrue($this->project->getTasks()->contains($task2));
        $this->assertEquals($this->project, $task1->getProject());
        $this->assertEquals($this->project, $task2->getProject());

        // Test adding same task twice (should not duplicate)
        $this->project->addTask($task1);
        $this->assertCount(2, $this->project->getTasks());

        // Test removing task
        $this->project->removeTask($task1);
        $this->assertCount(1, $this->project->getTasks());
        $this->assertFalse($this->project->getTasks()->contains($task1));
        $this->assertNull($task1->getProject());
    }

    public function testMemberManagement(): void
    {
        $user1 = new User();
        $user1->setEmail('<EMAIL>');
        $user1->setFirstName('John');
        $user1->setLastName('Doe');
        
        $user2 = new User();
        $user2->setEmail('<EMAIL>');
        $user2->setFirstName('Jane');
        $user2->setLastName('Smith');

        // Test adding members
        $this->project->addMember($user1);
        $this->project->addMember($user2);

        $this->assertCount(2, $this->project->getMembers());
        $this->assertTrue($this->project->getMembers()->contains($user1));
        $this->assertTrue($this->project->getMembers()->contains($user2));

        // Test adding same member twice (should not duplicate)
        $this->project->addMember($user1);
        $this->assertCount(2, $this->project->getMembers());

        // Test removing member
        $this->project->removeMember($user1);
        $this->assertCount(1, $this->project->getMembers());
        $this->assertFalse($this->project->getMembers()->contains($user1));
        $this->assertTrue($this->project->getMembers()->contains($user2));
    }

    public function testGetTotalCo2Emissions(): void
    {
        // Test with no tasks
        $this->assertEquals(0.0, $this->project->getTotalCo2Emissions());

        // Test with tasks
        $task1 = new Task();
        $task1->setType(Task::TYPE_OFFICE_LIGHT);
        $task1->setActualHours('2.0');
        
        $task2 = new Task();
        $task2->setType(Task::TYPE_TECHNICAL);
        $task2->setActualHours('4.0');

        $task3 = new Task();
        $task3->setType(Task::TYPE_ENERGY_INTENSIVE);
        $task3->setActualHours('1.0');

        $this->project->addTask($task1);
        $this->project->addTask($task2);
        $this->project->addTask($task3);

        $expectedCo2 = 
            (2.0 * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT]) +
            (4.0 * Task::CO2_RATES[Task::TYPE_TECHNICAL]) +
            (1.0 * Task::CO2_RATES[Task::TYPE_ENERGY_INTENSIVE]);

        $this->assertEquals($expectedCo2, $this->project->getTotalCo2Emissions());
    }

    public function testGetTotalCo2EmissionsWithMixedHours(): void
    {
        // Test with estimated hours when actual hours not set
        $task1 = new Task();
        $task1->setType(Task::TYPE_OFFICE_LIGHT);
        $task1->setEstimatedHours('3.0');
        
        $task2 = new Task();
        $task2->setType(Task::TYPE_TECHNICAL);
        $task2->setActualHours('2.0');

        $this->project->addTask($task1);
        $this->project->addTask($task2);

        $expectedCo2 = 
            (3.0 * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT]) +
            (2.0 * Task::CO2_RATES[Task::TYPE_TECHNICAL]);

        $this->assertEquals($expectedCo2, $this->project->getTotalCo2Emissions());
    }
}
