<?php

namespace App\Tests\Entity;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use DateTimeImmutable;
use PHPUnit\Framework\TestCase;

class ProjectTest extends TestCase
{
    public function testProjectCreation(): void
    {
        $project = new Project();

        $this->assertInstanceOf(DateTimeImmutable::class, $project->getCreatedAt());
        $this->assertNull($project->getUpdatedAt());
        $this->assertNull($project->getName());
        $this->assertNull($project->getDescription());
        $this->assertCount(0, $project->getTasks());
        $this->assertCount(0, $project->getMembers());
    }

    public function testProjectProperties(): void
    {
        $project = new Project();
        $updatedAt = new DateTimeImmutable();

        $project->setName('Test Project');
        $project->setDescription('Test Description');
        $project->setUpdatedAt($updatedAt);

        $this->assertEquals('Test Project', $project->getName());
        $this->assertEquals('Test Description', $project->getDescription());
        $this->assertEquals($updatedAt, $project->getUpdatedAt());
    }

    public function testAddTask(): void
    {
        $project = new Project();
        $task = new Task();

        $project->addTask($task);

        $this->assertCount(1, $project->getTasks());
        $this->assertTrue($project->getTasks()->contains($task));
        $this->assertEquals($project, $task->getProject());
    }

    public function testRemoveTask(): void
    {
        $project = new Project();
        $task = new Task();

        $project->addTask($task);
        $this->assertCount(1, $project->getTasks());

        $project->removeTask($task);
        $this->assertCount(0, $project->getTasks());
        $this->assertFalse($project->getTasks()->contains($task));
    }

    public function testAddTaskTwice(): void
    {
        $project = new Project();
        $task = new Task();

        $project->addTask($task);
        $project->addTask($task); // Should not add twice

        $this->assertCount(1, $project->getTasks());
    }

    public function testAddMember(): void
    {
        $project = new Project();
        $user = new User();

        $project->addMember($user);

        $this->assertCount(1, $project->getMembers());
        $this->assertTrue($project->getMembers()->contains($user));
    }

    public function testRemoveMember(): void
    {
        $project = new Project();
        $user = new User();

        $project->addMember($user);
        $this->assertCount(1, $project->getMembers());

        $project->removeMember($user);
        $this->assertCount(0, $project->getMembers());
        $this->assertFalse($project->getMembers()->contains($user));
    }

    public function testAddMemberTwice(): void
    {
        $project = new Project();
        $user = new User();

        $project->addMember($user);
        $project->addMember($user); // Should not add twice

        $this->assertCount(1, $project->getMembers());
    }

    public function testGetTotalCo2EmissionsEmpty(): void
    {
        $project = new Project();

        $this->assertEquals(0.0, $project->getTotalCo2Emissions());
    }

    public function testGetTotalCo2EmissionsWithTasks(): void
    {
        $project = new Project();

        $task1 = new Task();
        $task1->setType(Task::TYPE_OFFICE_LIGHT);
        $task1->setActualHours('8.0');

        $task2 = new Task();
        $task2->setType(Task::TYPE_TECHNICAL);
        $task2->setEstimatedHours('5.0');

        $task3 = new Task();
        $task3->setType(Task::TYPE_ENERGY_INTENSIVE);
        $task3->setActualHours('2.0');

        $project->addTask($task1);
        $project->addTask($task2);
        $project->addTask($task3);

        $expectedCo2 = (8.0 * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT]) +
                       (5.0 * Task::CO2_RATES[Task::TYPE_TECHNICAL]) +
                       (2.0 * Task::CO2_RATES[Task::TYPE_ENERGY_INTENSIVE]);

        $this->assertEquals($expectedCo2, $project->getTotalCo2Emissions());
    }

    public function testGetTotalCo2EmissionsWithTasksNoHours(): void
    {
        $project = new Project();

        $task1 = new Task();
        $task1->setType(Task::TYPE_OFFICE_LIGHT);
        // No hours set

        $task2 = new Task();
        $task2->setType(Task::TYPE_TECHNICAL);
        $task2->setActualHours('3.0');

        $project->addTask($task1);
        $project->addTask($task2);

        $expectedCo2 = 0.0 + (3.0 * Task::CO2_RATES[Task::TYPE_TECHNICAL]);

        $this->assertEquals($expectedCo2, $project->getTotalCo2Emissions());
    }

    public function testProjectWithMultipleMembers(): void
    {
        $project = new Project();

        $user1 = new User();
        $user1->setEmail('<EMAIL>');

        $user2 = new User();
        $user2->setEmail('<EMAIL>');

        $user3 = new User();
        $user3->setEmail('<EMAIL>');

        $project->addMember($user1);
        $project->addMember($user2);
        $project->addMember($user3);

        $this->assertCount(3, $project->getMembers());
        $this->assertTrue($project->getMembers()->contains($user1));
        $this->assertTrue($project->getMembers()->contains($user2));
        $this->assertTrue($project->getMembers()->contains($user3));
    }

    public function testProjectWithMultipleTasks(): void
    {
        $project = new Project();

        $tasks = [];

        for ($i = 1; $i <= 5; ++$i) {
            $task = new Task();
            $task->setTitle("Task $i");
            $task->setType(Task::TYPE_OFFICE_LIGHT);
            $task->setEstimatedHours('2.0');
            $tasks[] = $task;
            $project->addTask($task);
        }

        $this->assertCount(5, $project->getTasks());

        foreach ($tasks as $task) {
            $this->assertTrue($project->getTasks()->contains($task));
            $this->assertEquals($project, $task->getProject());
        }

        $expectedCo2 = 5 * 2.0 * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT];
        $this->assertEquals($expectedCo2, $project->getTotalCo2Emissions());
    }

    public function testSetCreatedAt(): void
    {
        $project = new Project();
        $customDate = new DateTimeImmutable('2023-01-01 10:00:00');

        $project->setCreatedAt($customDate);

        $this->assertEquals($customDate, $project->getCreatedAt());
    }

    public function testProjectId(): void
    {
        $project = new Project();

        // ID should be null before persistence
        $this->assertNull($project->getId());
    }
}
