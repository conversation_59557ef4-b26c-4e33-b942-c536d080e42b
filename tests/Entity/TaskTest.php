<?php

namespace App\Tests\Entity;

use App\Entity\Task;
use DateTimeImmutable;
use PHPUnit\Framework\TestCase;

class TaskTest extends TestCase
{
    public function testTaskCreation(): void
    {
        $task = new Task();

        $this->assertInstanceOf(DateTimeImmutable::class, $task->getCreatedAt());
        $this->assertNull($task->getUpdatedAt());
        $this->assertNull($task->getCompletedAt());
        $this->assertEquals(Task::STATUS_TODO, $task->getStatus());
        $this->assertEquals(Task::PRIORITY_MEDIUM, $task->getPriority());
    }

    public function testTaskStatusChange(): void
    {
        $task = new Task();

        $task->setStatus(Task::STATUS_DONE);

        $this->assertEquals(Task::STATUS_DONE, $task->getStatus());
        $this->assertInstanceOf(DateTimeImmutable::class, $task->getCompletedAt());
    }

    public function testCo2EmissionCalculation(): void
    {
        $task = new Task();
        $task->setType(Task::TYPE_OFFICE_LIGHT);
        $task->setActualHours('8.0');

        $expectedCo2 = 8.0 * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT];

        $this->assertEquals($expectedCo2, $task->getCo2Emission());
    }

    public function testIsOverdue(): void
    {
        $task = new Task();

        // Tâche sans date d'échéance
        $this->assertFalse($task->isOverdue());

        // Tâche avec date d'échéance dans le futur
        $task->setDueDate(new DateTimeImmutable('+1 day'));
        $this->assertFalse($task->isOverdue());

        // Tâche avec date d'échéance dans le passé
        $task->setDueDate(new DateTimeImmutable('-1 day'));
        $this->assertTrue($task->isOverdue());

        // Tâche terminée avec date d'échéance dans le passé
        $task->setStatus(Task::STATUS_DONE);
        $this->assertFalse($task->isOverdue());
    }

    public function testGetPriorities(): void
    {
        $priorities = Task::getPriorities();

        $this->assertIsArray($priorities);
        $this->assertArrayHasKey(Task::PRIORITY_LOW, $priorities);
        $this->assertArrayHasKey(Task::PRIORITY_MEDIUM, $priorities);
        $this->assertArrayHasKey(Task::PRIORITY_HIGH, $priorities);
        $this->assertArrayHasKey(Task::PRIORITY_URGENT, $priorities);
    }

    public function testGetStatuses(): void
    {
        $statuses = Task::getStatuses();

        $this->assertIsArray($statuses);
        $this->assertArrayHasKey(Task::STATUS_TODO, $statuses);
        $this->assertArrayHasKey(Task::STATUS_IN_PROGRESS, $statuses);
        $this->assertArrayHasKey(Task::STATUS_DONE, $statuses);
    }

    public function testGetTypes(): void
    {
        $types = Task::getTypes();

        $this->assertIsArray($types);
        $this->assertArrayHasKey(Task::TYPE_OFFICE_LIGHT, $types);
        $this->assertArrayHasKey(Task::TYPE_TECHNICAL, $types);
        $this->assertArrayHasKey(Task::TYPE_ENERGY_INTENSIVE, $types);
    }

    public function testSettersAndGetters(): void
    {
        $title = 'Test Task';
        $description = 'This is a test task';
        $priority = Task::PRIORITY_HIGH;
        $status = Task::STATUS_IN_PROGRESS;
        $type = Task::TYPE_TECHNICAL;
        $estimatedHours = '5.5';
        $actualHours = '6.0';
        $dueDate = new DateTimeImmutable('+1 week');

        $task = new Task();
        $task->setTitle($title);
        $task->setDescription($description);
        $task->setPriority($priority);
        $task->setStatus($status);
        $task->setType($type);
        $task->setEstimatedHours($estimatedHours);
        $task->setActualHours($actualHours);
        $task->setDueDate($dueDate);

        $this->assertEquals($title, $task->getTitle());
        $this->assertEquals($description, $task->getDescription());
        $this->assertEquals($priority, $task->getPriority());
        $this->assertEquals($status, $task->getStatus());
        $this->assertEquals($type, $task->getType());
        $this->assertEquals($estimatedHours, $task->getEstimatedHours());
        $this->assertEquals($actualHours, $task->getActualHours());
        $this->assertEquals($dueDate, $task->getDueDate());
    }

    public function testCo2EmissionWithEstimatedHours(): void
    {
        $task = new Task();
        $task->setType(Task::TYPE_TECHNICAL);
        $task->setEstimatedHours('4.0');

        $expectedCo2 = 4.0 * Task::CO2_RATES[Task::TYPE_TECHNICAL];

        $this->assertEquals($expectedCo2, $task->getCo2Emission());
    }

    public function testCo2EmissionPrioritizesActualHours(): void
    {
        $task = new Task();
        $task->setType(Task::TYPE_TECHNICAL);
        $task->setEstimatedHours('4.0');
        $task->setActualHours('6.0');

        $expectedCo2 = 6.0 * Task::CO2_RATES[Task::TYPE_TECHNICAL];

        $this->assertEquals($expectedCo2, $task->getCo2Emission());
    }

    public function testCo2EmissionWithNoHours(): void
    {
        $task = new Task();
        $task->setType(Task::TYPE_TECHNICAL);

        $this->assertEquals(0.0, $task->getCo2Emission());
    }

    public function testGetCo2Rate(): void
    {
        $task = new Task();

        $task->setType(Task::TYPE_OFFICE_LIGHT);
        $this->assertEquals(Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT], $task->getCo2Rate());

        $task->setType(Task::TYPE_TECHNICAL);
        $this->assertEquals(Task::CO2_RATES[Task::TYPE_TECHNICAL], $task->getCo2Rate());

        $task->setType(Task::TYPE_ENERGY_INTENSIVE);
        $this->assertEquals(Task::CO2_RATES[Task::TYPE_ENERGY_INTENSIVE], $task->getCo2Rate());
    }

    public function testStatusChangeToCompleted(): void
    {
        $task = new Task();
        $task->setStatus(Task::STATUS_DONE);

        $this->assertEquals(Task::STATUS_DONE, $task->getStatus());
        $this->assertInstanceOf(DateTimeImmutable::class, $task->getCompletedAt());
    }

    public function testStatusChangeFromCompleted(): void
    {
        $task = new Task();
        $task->setStatus(Task::STATUS_DONE);
        $this->assertNotNull($task->getCompletedAt());

        $task->setStatus(Task::STATUS_IN_PROGRESS);
        $this->assertEquals(Task::STATUS_IN_PROGRESS, $task->getStatus());
        // completedAt should remain set until explicitly cleared
        $this->assertNotNull($task->getCompletedAt());
    }

    public function testUpdatedAtIsSetOnUpdate(): void
    {
        $task = new Task();
        $originalUpdatedAt = $task->getUpdatedAt();

        $task->setUpdatedAt(new DateTimeImmutable());

        $this->assertNotEquals($originalUpdatedAt, $task->getUpdatedAt());
        $this->assertInstanceOf(DateTimeImmutable::class, $task->getUpdatedAt());
    }

    public function testProjectAndUserRelations(): void
    {
        $task = new Task();
        $project = new \App\Entity\Project();
        $user = new \App\Entity\User();

        $task->setProject($project);
        $task->setAssignedTo($user);

        $this->assertEquals($project, $task->getProject());
        $this->assertEquals($user, $task->getAssignedTo());
    }

    public function testTaskWithDifferentTypes(): void
    {
        $task = new Task();

        // Test avec différents types
        $types = [Task::TYPE_OFFICE_LIGHT, Task::TYPE_TECHNICAL, Task::TYPE_ENERGY_INTENSIVE];

        foreach ($types as $type) {
            $task->setType($type);
            $this->assertEquals($type, $task->getType());
            $this->assertArrayHasKey($type, Task::CO2_RATES);
        }
    }

    public function testTaskWithDifferentPriorities(): void
    {
        $task = new Task();

        // Test avec différentes priorités
        $priorities = [Task::PRIORITY_LOW, Task::PRIORITY_MEDIUM, Task::PRIORITY_HIGH, Task::PRIORITY_URGENT];

        foreach ($priorities as $priority) {
            $task->setPriority($priority);
            $this->assertEquals($priority, $task->getPriority());
        }
    }
}
