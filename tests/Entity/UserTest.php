<?php

namespace App\Tests\Entity;

use App\Entity\Task;
use App\Entity\User;
use LogicException;
use PHPUnit\Framework\TestCase;

class UserTest extends TestCase
{
    private User $user;

    protected function setUp(): void
    {
        $this->user = new User();
    }

    public function testUserCreation(): void
    {
        $this->assertNull($this->user->getId());
        $this->assertNull($this->user->getEmail());
        $this->assertNull($this->user->getFirstName());
        $this->assertNull($this->user->getLastName());
        $this->assertNull($this->user->getPassword());
        $this->assertEquals(['ROLE_USER'], $this->user->getRoles());
        $this->assertCount(0, $this->user->getTasks());
    }

    public function testSettersAndGetters(): void
    {
        $email = '<EMAIL>';
        $firstName = 'John';
        $lastName = 'Doe';
        $password = 'hashed_password';

        $this->user->setEmail($email);
        $this->user->setFirstName($firstName);
        $this->user->setLastName($lastName);
        $this->user->setPassword($password);

        $this->assertEquals($email, $this->user->getEmail());
        $this->assertEquals($firstName, $this->user->getFirstName());
        $this->assertEquals($lastName, $this->user->getLastName());
        $this->assertEquals($password, $this->user->getPassword());
    }

    public function testGetUserIdentifier(): void
    {
        $email = '<EMAIL>';
        $this->user->setEmail($email);

        $this->assertEquals($email, $this->user->getUserIdentifier());
    }

    public function testGetUserIdentifierThrowsExceptionWhenEmailEmpty(): void
    {
        $this->expectException(LogicException::class);
        $this->expectExceptionMessage('User email cannot be empty');

        $this->user->getUserIdentifier();
    }

    public function testGetFullName(): void
    {
        $this->user->setFirstName('John');
        $this->user->setLastName('Doe');

        $this->assertEquals('John Doe', $this->user->getFullName());
    }

    public function testRoles(): void
    {
        // Test default role
        $this->assertEquals(['ROLE_USER'], $this->user->getRoles());

        // Test adding roles
        $this->user->setRoles(['ROLE_ADMIN', 'ROLE_USER']);
        $roles = $this->user->getRoles();
        
        $this->assertContains('ROLE_USER', $roles);
        $this->assertContains('ROLE_ADMIN', $roles);
        $this->assertCount(2, array_unique($roles));
    }

    public function testTaskManagement(): void
    {
        $task1 = new Task();
        $task1->setTitle('Task 1');
        
        $task2 = new Task();
        $task2->setTitle('Task 2');

        // Test adding tasks
        $this->user->addTask($task1);
        $this->user->addTask($task2);

        $this->assertCount(2, $this->user->getTasks());
        $this->assertTrue($this->user->getTasks()->contains($task1));
        $this->assertTrue($this->user->getTasks()->contains($task2));
        $this->assertEquals($this->user, $task1->getAssignedTo());
        $this->assertEquals($this->user, $task2->getAssignedTo());

        // Test adding same task twice (should not duplicate)
        $this->user->addTask($task1);
        $this->assertCount(2, $this->user->getTasks());

        // Test removing task
        $this->user->removeTask($task1);
        $this->assertCount(1, $this->user->getTasks());
        $this->assertFalse($this->user->getTasks()->contains($task1));
        $this->assertNull($task1->getAssignedTo());
    }

    public function testEraseCredentials(): void
    {
        // This method should be empty for now
        $this->user->eraseCredentials();
        $this->assertTrue(true); // Just ensure no exception is thrown
    }
}
