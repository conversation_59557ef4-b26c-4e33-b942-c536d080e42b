<?php

namespace App\Tests\Entity;

use App\Entity\Task;
use App\Entity\User;
use LogicException;
use PHPUnit\Framework\TestCase;

class UserTest extends TestCase
{
    public function testUserCreation(): void
    {
        $user = new User();

        $this->assertNull($user->getId());
        $this->assertNull($user->getEmail());
        $this->assertNull($user->getFirstName());
        $this->assertNull($user->getLastName());
        $this->assertNull($user->getPassword());
        $this->assertEquals(['ROLE_USER'], $user->getRoles());
        $this->assertCount(0, $user->getTasks());
    }

    public function testUserProperties(): void
    {
        $user = new User();

        $user->setEmail('<EMAIL>');
        $user->setFirstName('John');
        $user->setLastName('Doe');
        $user->setPassword('hashed_password');

        $this->assertEquals('<EMAIL>', $user->getEmail());
        $this->assertEquals('John', $user->getFirstName());
        $this->assertEquals('Doe', $user->getLastName());
        $this->assertEquals('hashed_password', $user->getPassword());
    }

    public function testGetUserIdentifier(): void
    {
        $user = new User();
        $user->setEmail('<EMAIL>');

        $this->assertEquals('<EMAIL>', $user->getUserIdentifier());
    }

    public function testGetUserIdentifierWithEmptyEmail(): void
    {
        $user = new User();

        $this->expectException(LogicException::class);
        $this->expectExceptionMessage('User email cannot be empty');

        $user->getUserIdentifier();
    }

    public function testGetFullName(): void
    {
        $user = new User();
        $user->setFirstName('John');
        $user->setLastName('Doe');

        $this->assertEquals('John Doe', $user->getFullName());
    }

    public function testGetFullNameWithOnlyFirstName(): void
    {
        $user = new User();
        $user->setFirstName('John');

        $this->assertEquals('John ', $user->getFullName());
    }

    public function testGetFullNameWithOnlyLastName(): void
    {
        $user = new User();
        $user->setLastName('Doe');

        $this->assertEquals(' Doe', $user->getFullName());
    }

    public function testGetFullNameWithEmptyNames(): void
    {
        $user = new User();

        $this->assertEquals(' ', $user->getFullName());
    }

    public function testGetRolesDefault(): void
    {
        $user = new User();

        $roles = $user->getRoles();

        $this->assertContains('ROLE_USER', $roles);
        $this->assertCount(1, $roles);
    }

    public function testSetRoles(): void
    {
        $user = new User();
        $roles = ['ROLE_ADMIN', 'ROLE_MANAGER'];

        $user->setRoles($roles);

        $userRoles = $user->getRoles();
        $this->assertContains('ROLE_USER', $userRoles); // Always present
        $this->assertContains('ROLE_ADMIN', $userRoles);
        $this->assertContains('ROLE_MANAGER', $userRoles);
        $this->assertCount(3, $userRoles);
    }

    public function testSetRolesWithDuplicates(): void
    {
        $user = new User();
        $roles = ['ROLE_ADMIN', 'ROLE_USER', 'ROLE_ADMIN']; // Duplicates

        $user->setRoles($roles);

        $userRoles = $user->getRoles();
        $this->assertContains('ROLE_USER', $userRoles);
        $this->assertContains('ROLE_ADMIN', $userRoles);
        $this->assertCount(2, $userRoles); // No duplicates
    }

    public function testAddTask(): void
    {
        $user = new User();
        $task = new Task();

        $user->addTask($task);

        $this->assertCount(1, $user->getTasks());
        $this->assertTrue($user->getTasks()->contains($task));
        $this->assertEquals($user, $task->getAssignedTo());
    }

    public function testRemoveTask(): void
    {
        $user = new User();
        $task = new Task();

        $user->addTask($task);
        $this->assertCount(1, $user->getTasks());

        $user->removeTask($task);
        $this->assertCount(0, $user->getTasks());
        $this->assertFalse($user->getTasks()->contains($task));
    }

    public function testAddTaskTwice(): void
    {
        $user = new User();
        $task = new Task();

        $user->addTask($task);
        $user->addTask($task); // Should not add twice

        $this->assertCount(1, $user->getTasks());
    }

    public function testUserWithMultipleTasks(): void
    {
        $user = new User();

        $tasks = [];

        for ($i = 1; $i <= 3; ++$i) {
            $task = new Task();
            $task->setTitle("Task $i");
            $tasks[] = $task;
            $user->addTask($task);
        }

        $this->assertCount(3, $user->getTasks());

        foreach ($tasks as $task) {
            $this->assertTrue($user->getTasks()->contains($task));
            $this->assertEquals($user, $task->getAssignedTo());
        }
    }

    public function testEraseCredentials(): void
    {
        $user = new User();

        // This method should exist but do nothing in our implementation
        $user->eraseCredentials();

        // No assertion needed as the method should be empty
        $this->assertTrue(true);
    }

    public function testUserInterfaceImplementation(): void
    {
        $user = new User();
        $user->setEmail('<EMAIL>');

        // Test UserInterface methods
        $this->assertEquals('<EMAIL>', $user->getUserIdentifier());
        $this->assertIsArray($user->getRoles());

        // Test PasswordAuthenticatedUserInterface methods
        $user->setPassword('test_password');
        $this->assertEquals('test_password', $user->getPassword());
    }

    public function testUserEmailValidation(): void
    {
        $user = new User();

        // Test various email formats
        $validEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ];

        foreach ($validEmails as $email) {
            $user->setEmail($email);
            $this->assertEquals($email, $user->getEmail());
            $this->assertEquals($email, $user->getUserIdentifier());
        }
    }

    public function testUserNameHandling(): void
    {
        $user = new User();

        // Test with special characters
        $user->setFirstName('Jean-Pierre');
        $user->setLastName("O'Connor");

        $this->assertEquals('Jean-Pierre', $user->getFirstName());
        $this->assertEquals("O'Connor", $user->getLastName());
        $this->assertEquals("Jean-Pierre O'Connor", $user->getFullName());
    }

    public function testUserRoleManagement(): void
    {
        $user = new User();

        // Test empty roles array
        $user->setRoles([]);
        $this->assertEquals(['ROLE_USER'], $user->getRoles());

        // Test with null values (should be filtered out)
        $user->setRoles(['ROLE_ADMIN', null, 'ROLE_MANAGER']);
        $roles = $user->getRoles();
        $this->assertContains('ROLE_USER', $roles);
        $this->assertContains('ROLE_ADMIN', $roles);
        $this->assertContains('ROLE_MANAGER', $roles);
        $this->assertNotContains(null, $roles);
    }
}
