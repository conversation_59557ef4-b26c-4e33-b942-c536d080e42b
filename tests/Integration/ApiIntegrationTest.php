<?php

namespace App\Tests\Integration;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class ApiIntegrationTest extends WebTestCase
{
    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        $kernel = self::bootKernel();
        $this->entityManager = $kernel->getContainer()
            ->get('doctrine')
            ->getManager();
    }

    protected function tearDown(): void
    {
        $this->entityManager->createQuery('DELETE FROM App\Entity\Task')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\Project')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\User')->execute();
        
        parent::tearDown();
    }

    public function testHealthEndpointWithRealDatabase(): void
    {
        $client = static::createClient();

        // Créer quelques données pour s'assurer que la DB fonctionne
        $this->createTestData();

        $client->request('GET', '/health');

        $this->assertResponseIsSuccessful();
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertEquals('healthy', $data['status']);
        $this->assertEquals('ok', $data['checks']['database']);
        $this->assertArrayHasKey('php_version', $data['checks']);
        $this->assertArrayHasKey('symfony_version', $data['checks']);
        $this->assertArrayHasKey('memory_usage', $data['checks']);
        $this->assertArrayHasKey('timestamp', $data);
    }

    public function testHealthEndpointPerformance(): void
    {
        $client = static::createClient();

        $startTime = microtime(true);
        $client->request('GET', '/health');
        $endTime = microtime(true);

        $responseTime = ($endTime - $startTime) * 1000; // en millisecondes

        $this->assertResponseIsSuccessful();
        $this->assertLessThan(1000, $responseTime, 'Health check should respond in less than 1 second');
    }

    public function testHealthEndpointUnderLoad(): void
    {
        $client = static::createClient();

        // Simuler plusieurs requêtes simultanées
        $responses = [];
        for ($i = 0; $i < 10; $i++) {
            $client->request('GET', '/health');
            $responses[] = $client->getResponse()->getStatusCode();
        }

        // Toutes les requêtes devraient réussir
        foreach ($responses as $statusCode) {
            $this->assertEquals(200, $statusCode);
        }
    }

    public function testApplicationRoutesAccessibility(): void
    {
        $client = static::createClient();

        // Tester les routes principales
        $routes = [
            '/' => 200,
            '/dashboard' => 200,
            '/task/' => 200,
            '/task/new' => 200,
            '/project/' => 200,
            '/project/new' => 200,
            '/health' => 200,
        ];

        foreach ($routes as $route => $expectedStatus) {
            $client->request('GET', $route);
            $this->assertEquals(
                $expectedStatus, 
                $client->getResponse()->getStatusCode(),
                "Route {$route} should return status {$expectedStatus}"
            );
        }
    }

    public function testApplicationWithRealData(): void
    {
        $client = static::createClient();

        // Créer des données réalistes
        $this->createRealisticTestData();

        // Tester le dashboard avec des données
        $client->request('GET', '/dashboard');
        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('.stats-card');
        $this->assertSelectorExists('.co2-stats');

        // Tester la liste des tâches
        $client->request('GET', '/task/');
        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('.task-list');

        // Tester la liste des projets
        $client->request('GET', '/project/');
        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('.project-list');
    }

    public function testCo2CalculationIntegration(): void
    {
        $client = static::createClient();

        // Créer un projet avec des tâches ayant des émissions CO2 connues
        $project = $this->createTestProject('CO2 Test Project');
        $user = $this->createTestUser();

        $tasks = [
            ['type' => Task::TYPE_OFFICE_LIGHT, 'hours' => '10.0'],
            ['type' => Task::TYPE_TECHNICAL, 'hours' => '5.0'],
            ['type' => Task::TYPE_ENERGY_INTENSIVE, 'hours' => '2.0'],
        ];

        $expectedTotalCo2 = 0;
        foreach ($tasks as $i => $taskData) {
            $task = new Task();
            $task->setTitle('CO2 Task ' . ($i + 1));
            $task->setType($taskData['type']);
            $task->setActualHours($taskData['hours']);
            $task->setProject($project);
            $task->setAssignedTo($user);
            $this->entityManager->persist($task);

            $expectedTotalCo2 += (float)$taskData['hours'] * Task::CO2_RATES[$taskData['type']];
        }

        $this->entityManager->flush();

        // Vérifier les calculs sur la page du projet
        $client->request('GET', '/project/' . $project->getId());
        $this->assertResponseIsSuccessful();

        // Vérifier que les émissions CO2 sont affichées
        $this->assertSelectorExists('.co2-stats');
        
        // Vérifier le calcul dans l'entité
        $this->entityManager->refresh($project);
        $this->assertEquals($expectedTotalCo2, $project->getTotalCo2Emissions());
    }

    public function testFormValidationIntegration(): void
    {
        $client = static::createClient();

        // Tester la validation des formulaires de tâche
        $crawler = $client->request('GET', '/task/new');
        $this->assertResponseIsSuccessful();

        // Soumettre un formulaire vide
        $form = $crawler->selectButton('Créer')->form();
        $client->submit($form);

        // Devrait rester sur la page avec des erreurs
        $this->assertResponseIsSuccessful();
        $this->assertSelectorExists('.form-error, .invalid-feedback');

        // Tester avec des données valides
        $form = $crawler->selectButton('Créer')->form([
            'task[title]' => 'Valid Task Title',
            'task[description]' => 'Valid description',
            'task[priority]' => Task::PRIORITY_MEDIUM,
            'task[type]' => Task::TYPE_OFFICE_LIGHT,
        ]);

        $client->submit($form);
        $this->assertResponseRedirects('/task/');
    }

    public function testDatabaseTransactionIntegrity(): void
    {
        $client = static::createClient();

        // Créer un projet
        $crawler = $client->request('GET', '/project/new');
        $form = $crawler->selectButton('Créer')->form([
            'project[name]' => 'Transaction Test Project',
            'project[description]' => 'Testing database transactions',
        ]);

        $client->submit($form);
        $this->assertResponseRedirects('/project/');

        // Vérifier que le projet existe
        $project = $this->entityManager->getRepository(Project::class)
            ->findOneBy(['name' => 'Transaction Test Project']);
        $this->assertNotNull($project);

        // Supprimer le projet
        $crawler = $client->request('GET', '/project/' . $project->getId());
        $form = $crawler->selectButton('Supprimer')->form();
        $client->submit($form);

        // Vérifier que le projet a été supprimé
        $deletedProject = $this->entityManager->getRepository(Project::class)
            ->find($project->getId());
        $this->assertNull($deletedProject);
    }

    public function testSessionAndStateManagement(): void
    {
        $client = static::createClient();

        // Créer une tâche
        $task = $this->createTestTask();

        // Modifier le statut plusieurs fois dans la même session
        for ($i = 0; $i < 3; $i++) {
            $crawler = $client->request('GET', '/task/' . $task->getId());
            $form = $crawler->filter('form[action*="toggle-status"]')->form();
            $client->submit($form);
            
            $this->assertResponseRedirects();
            $client->followRedirect();
            $this->assertResponseIsSuccessful();
        }

        // Vérifier l'état final
        $this->entityManager->refresh($task);
        $this->assertEquals(Task::STATUS_DONE, $task->getStatus());
    }

    private function createTestData(): void
    {
        $project = $this->createTestProject();
        $user = $this->createTestUser();
        
        $task = new Task();
        $task->setTitle('Health Check Task');
        $task->setProject($project);
        $task->setAssignedTo($user);
        $this->entityManager->persist($task);
        $this->entityManager->flush();
    }

    private function createRealisticTestData(): void
    {
        // Créer 2 projets
        $project1 = $this->createTestProject('E-commerce Platform');
        $project2 = $this->createTestProject('Mobile App Development');

        // Créer 2 utilisateurs
        $user1 = $this->createTestUser('<EMAIL>', 'John', 'Developer');
        $user2 = $this->createTestUser('<EMAIL>', 'Jane', 'Designer');

        // Ajouter des membres aux projets
        $project1->addMember($user1);
        $project1->addMember($user2);
        $project2->addMember($user1);

        // Créer des tâches réalistes
        $tasksData = [
            // Projet 1
            ['title' => 'Setup Database Schema', 'type' => Task::TYPE_TECHNICAL, 'hours' => '8.0', 'status' => Task::STATUS_DONE, 'project' => $project1, 'user' => $user1],
            ['title' => 'Design User Interface', 'type' => Task::TYPE_OFFICE_LIGHT, 'hours' => '12.0', 'status' => Task::STATUS_DONE, 'project' => $project1, 'user' => $user2],
            ['title' => 'Implement Payment Gateway', 'type' => Task::TYPE_TECHNICAL, 'hours' => '16.0', 'status' => Task::STATUS_IN_PROGRESS, 'project' => $project1, 'user' => $user1],
            ['title' => 'Performance Testing', 'type' => Task::TYPE_ENERGY_INTENSIVE, 'hours' => '4.0', 'status' => Task::STATUS_TODO, 'project' => $project1, 'user' => $user1],
            
            // Projet 2
            ['title' => 'Mobile App Wireframes', 'type' => Task::TYPE_OFFICE_LIGHT, 'hours' => '6.0', 'status' => Task::STATUS_DONE, 'project' => $project2, 'user' => $user2],
            ['title' => 'API Development', 'type' => Task::TYPE_TECHNICAL, 'hours' => '20.0', 'status' => Task::STATUS_IN_PROGRESS, 'project' => $project2, 'user' => $user1],
            ['title' => 'Load Testing', 'type' => Task::TYPE_ENERGY_INTENSIVE, 'hours' => '3.0', 'status' => Task::STATUS_TODO, 'project' => $project2, 'user' => $user1],
        ];

        foreach ($tasksData as $taskData) {
            $task = new Task();
            $task->setTitle($taskData['title']);
            $task->setType($taskData['type']);
            $task->setActualHours($taskData['hours']);
            $task->setStatus($taskData['status']);
            $task->setProject($taskData['project']);
            $task->setAssignedTo($taskData['user']);
            $task->setPriority(Task::PRIORITY_MEDIUM);
            $this->entityManager->persist($task);
        }

        $this->entityManager->flush();
    }

    private function createTestProject(string $name = 'Test Project'): Project
    {
        $project = new Project();
        $project->setName($name);
        $project->setDescription('Integration test project');
        $this->entityManager->persist($project);
        $this->entityManager->flush();

        return $project;
    }

    private function createTestUser(string $email = '<EMAIL>', string $firstName = 'Test', string $lastName = 'User'): User
    {
        $user = new User();
        $user->setEmail($email);
        $user->setFirstName($firstName);
        $user->setLastName($lastName);
        $user->setPassword('password');
        $this->entityManager->persist($user);
        $this->entityManager->flush();

        return $user;
    }

    private function createTestTask(): Task
    {
        $project = $this->createTestProject();
        $user = $this->createTestUser();

        $task = new Task();
        $task->setTitle('Integration Test Task');
        $task->setStatus(Task::STATUS_TODO);
        $task->setProject($project);
        $task->setAssignedTo($user);
        $this->entityManager->persist($task);
        $this->entityManager->flush();

        return $task;
    }
}
