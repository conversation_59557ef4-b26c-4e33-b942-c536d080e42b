<?php

namespace App\Tests\Integration;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class TaskManagementIntegrationTest extends WebTestCase
{
    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        $kernel = self::bootKernel();
        $this->entityManager = $kernel->getContainer()
            ->get('doctrine')
            ->getManager();
    }

    protected function tearDown(): void
    {
        $this->entityManager->createQuery('DELETE FROM App\Entity\Task')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\Project')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\User')->execute();
        
        parent::tearDown();
    }

    public function testCompleteTaskManagementWorkflow(): void
    {
        $client = static::createClient();

        // 1. Créer un projet
        $crawler = $client->request('GET', '/project/new');
        $this->assertResponseIsSuccessful();

        $form = $crawler->selectButton('Créer')->form([
            'project[name]' => 'Integration Test Project',
            'project[description]' => 'A project for integration testing',
        ]);

        $client->submit($form);
        $this->assertResponseRedirects('/project/');

        // Vérifier que le projet a été créé
        $project = $this->entityManager->getRepository(Project::class)
            ->findOneBy(['name' => 'Integration Test Project']);
        $this->assertNotNull($project);

        // 2. Créer une tâche dans ce projet
        $crawler = $client->request('GET', '/task/new');
        $this->assertResponseIsSuccessful();

        $form = $crawler->selectButton('Créer')->form([
            'task[title]' => 'Integration Test Task',
            'task[description]' => 'A task for integration testing',
            'task[priority]' => Task::PRIORITY_HIGH,
            'task[type]' => Task::TYPE_TECHNICAL,
            'task[estimatedHours]' => '5.0',
        ]);

        $client->submit($form);
        $this->assertResponseRedirects('/task/');

        // Vérifier que la tâche a été créée
        $task = $this->entityManager->getRepository(Task::class)
            ->findOneBy(['title' => 'Integration Test Task']);
        $this->assertNotNull($task);
        $this->assertEquals(Task::STATUS_TODO, $task->getStatus());

        // 3. Modifier le statut de la tâche (TODO -> IN_PROGRESS)
        $crawler = $client->request('GET', '/task/' . $task->getId());
        $this->assertResponseIsSuccessful();

        $form = $crawler->filter('form[action*="toggle-status"]')->form();
        $client->submit($form);
        $this->assertResponseRedirects();

        // Vérifier le changement de statut
        $this->entityManager->refresh($task);
        $this->assertEquals(Task::STATUS_IN_PROGRESS, $task->getStatus());

        // 4. Modifier la tâche pour ajouter les heures réelles
        $crawler = $client->request('GET', '/task/' . $task->getId() . '/edit');
        $this->assertResponseIsSuccessful();

        $form = $crawler->selectButton('Modifier')->form([
            'task[actualHours]' => '6.5',
        ]);

        $client->submit($form);
        $this->assertResponseRedirects('/task/');

        // Vérifier la mise à jour
        $this->entityManager->refresh($task);
        $this->assertEquals('6.5', $task->getActualHours());

        // 5. Marquer la tâche comme terminée
        $crawler = $client->request('GET', '/task/' . $task->getId());
        $form = $crawler->filter('form[action*="toggle-status"]')->form();
        $client->submit($form);

        // Vérifier que la tâche est terminée
        $this->entityManager->refresh($task);
        $this->assertEquals(Task::STATUS_DONE, $task->getStatus());
        $this->assertNotNull($task->getCompletedAt());

        // 6. Vérifier que les statistiques sont mises à jour sur le dashboard
        $client->request('GET', '/dashboard');
        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('.completed-tasks', '1');
        $this->assertSelectorExists('.co2-stats');
    }

    public function testProjectWithMultipleTasksWorkflow(): void
    {
        $client = static::createClient();

        // Créer un projet avec plusieurs tâches
        $project = $this->createTestProject();
        $user = $this->createTestUser();

        // Créer plusieurs tâches avec différents types et statuts
        $tasks = [
            ['title' => 'Office Task', 'type' => Task::TYPE_OFFICE_LIGHT, 'hours' => '2.0', 'status' => Task::STATUS_DONE],
            ['title' => 'Technical Task', 'type' => Task::TYPE_TECHNICAL, 'hours' => '4.0', 'status' => Task::STATUS_IN_PROGRESS],
            ['title' => 'Energy Task', 'type' => Task::TYPE_ENERGY_INTENSIVE, 'hours' => '1.0', 'status' => Task::STATUS_TODO],
        ];

        foreach ($tasks as $taskData) {
            $task = new Task();
            $task->setTitle($taskData['title']);
            $task->setType($taskData['type']);
            $task->setActualHours($taskData['hours']);
            $task->setStatus($taskData['status']);
            $task->setProject($project);
            $task->setAssignedTo($user);
            $this->entityManager->persist($task);
        }

        $this->entityManager->flush();

        // Vérifier la page du projet
        $client->request('GET', '/project/' . $project->getId());
        $this->assertResponseIsSuccessful();

        // Vérifier que les statistiques CO2 sont affichées
        $this->assertSelectorExists('.co2-stats');
        $this->assertSelectorTextContains('.total-co2', 'kg CO2');

        // Vérifier que toutes les tâches sont listées
        $this->assertSelectorTextContains('.task-list', 'Office Task');
        $this->assertSelectorTextContains('.task-list', 'Technical Task');
        $this->assertSelectorTextContains('.task-list', 'Energy Task');

        // Calculer les émissions CO2 attendues
        $expectedCo2 = 
            (2.0 * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT]) +
            (4.0 * Task::CO2_RATES[Task::TYPE_TECHNICAL]) +
            (1.0 * Task::CO2_RATES[Task::TYPE_ENERGY_INTENSIVE]);

        $this->entityManager->refresh($project);
        $this->assertEquals($expectedCo2, $project->getTotalCo2Emissions());
    }

    public function testDashboardStatisticsIntegration(): void
    {
        $client = static::createClient();

        // Créer des données de test
        $this->createTestDataForDashboard();

        // Accéder au dashboard
        $client->request('GET', '/dashboard');
        $this->assertResponseIsSuccessful();

        // Vérifier les statistiques générales
        $this->assertSelectorTextContains('.total-tasks', '6');
        $this->assertSelectorTextContains('.completed-tasks', '2');
        $this->assertSelectorTextContains('.in-progress-tasks', '2');
        $this->assertSelectorTextContains('.todo-tasks', '2');

        // Vérifier les statistiques CO2
        $this->assertSelectorExists('.co2-stats');
        $this->assertSelectorExists('.co2-by-type');

        // Vérifier les projets avec statistiques
        $this->assertSelectorExists('.projects-with-co2');

        // Vérifier les tâches récentes
        $this->assertSelectorExists('.recent-tasks');
    }

    public function testTaskStatusTransitionWorkflow(): void
    {
        $client = static::createClient();

        // Créer une tâche
        $task = $this->createTestTask();

        // Tester la transition complète TODO -> IN_PROGRESS -> DONE -> TODO
        $statuses = [Task::STATUS_TODO, Task::STATUS_IN_PROGRESS, Task::STATUS_DONE, Task::STATUS_TODO];

        for ($i = 0; $i < 3; $i++) {
            $this->assertEquals($statuses[$i], $task->getStatus());

            // Changer le statut
            $crawler = $client->request('GET', '/task/' . $task->getId());
            $form = $crawler->filter('form[action*="toggle-status"]')->form();
            $client->submit($form);

            $this->entityManager->refresh($task);
            $this->assertEquals($statuses[$i + 1], $task->getStatus());

            // Vérifier completedAt pour le statut DONE
            if ($statuses[$i + 1] === Task::STATUS_DONE) {
                $this->assertNotNull($task->getCompletedAt());
            } elseif ($statuses[$i + 1] === Task::STATUS_TODO && $statuses[$i] === Task::STATUS_DONE) {
                // Quand on revient de DONE à TODO, completedAt devrait être null
                $this->assertNull($task->getCompletedAt());
            }
        }
    }

    public function testHealthCheckIntegration(): void
    {
        $client = static::createClient();

        // Tester le health check
        $client->request('GET', '/health');
        $this->assertResponseIsSuccessful();

        $response = $client->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertEquals('healthy', $data['status']);
        $this->assertEquals('ok', $data['checks']['database']);
        $this->assertEquals('ok', $data['checks']['var_writable']);
    }

    private function createTestProject(): Project
    {
        $project = new Project();
        $project->setName('Integration Test Project');
        $project->setDescription('Test Description');
        $this->entityManager->persist($project);
        $this->entityManager->flush();

        return $project;
    }

    private function createTestUser(): User
    {
        $user = new User();
        $user->setEmail('<EMAIL>');
        $user->setFirstName('Integration');
        $user->setLastName('Test');
        $user->setPassword('password');
        $this->entityManager->persist($user);
        $this->entityManager->flush();

        return $user;
    }

    private function createTestTask(): Task
    {
        $project = $this->createTestProject();
        $user = $this->createTestUser();

        $task = new Task();
        $task->setTitle('Integration Test Task');
        $task->setStatus(Task::STATUS_TODO);
        $task->setProject($project);
        $task->setAssignedTo($user);
        $this->entityManager->persist($task);
        $this->entityManager->flush();

        return $task;
    }

    private function createTestDataForDashboard(): void
    {
        $project1 = $this->createTestProject();
        $project2 = new Project();
        $project2->setName('Second Project');
        $this->entityManager->persist($project2);

        $user = $this->createTestUser();

        // Créer 6 tâches avec différents statuts et types
        $taskData = [
            ['status' => Task::STATUS_TODO, 'type' => Task::TYPE_OFFICE_LIGHT, 'hours' => '1.0'],
            ['status' => Task::STATUS_TODO, 'type' => Task::TYPE_TECHNICAL, 'hours' => '2.0'],
            ['status' => Task::STATUS_IN_PROGRESS, 'type' => Task::TYPE_OFFICE_LIGHT, 'hours' => '1.5'],
            ['status' => Task::STATUS_IN_PROGRESS, 'type' => Task::TYPE_ENERGY_INTENSIVE, 'hours' => '0.5'],
            ['status' => Task::STATUS_DONE, 'type' => Task::TYPE_TECHNICAL, 'hours' => '3.0'],
            ['status' => Task::STATUS_DONE, 'type' => Task::TYPE_ENERGY_INTENSIVE, 'hours' => '1.0'],
        ];

        foreach ($taskData as $i => $data) {
            $task = new Task();
            $task->setTitle('Dashboard Task ' . ($i + 1));
            $task->setStatus($data['status']);
            $task->setType($data['type']);
            $task->setActualHours($data['hours']);
            $task->setProject($i < 3 ? $project1 : $project2);
            $task->setAssignedTo($user);
            $this->entityManager->persist($task);
        }

        $this->entityManager->flush();
    }
}
