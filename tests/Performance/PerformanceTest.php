<?php

namespace App\Tests\Performance;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use PHPUnit\Framework\TestCase;

class PerformanceTest extends TestCase
{
    public function testTaskCreationPerformance(): void
    {
        $iterations = 1000;
        $start = microtime(true);

        for ($i = 0; $i < $iterations; ++$i) {
            $task = new Task();
            $task->setTitle("Task $i");
            $task->setDescription("Description for task $i");
            $task->setStatus(Task::STATUS_TODO);
            $task->setPriority(Task::PRIORITY_MEDIUM);
            $task->setType(Task::TYPE_TECHNICAL);
        }

        $end = microtime(true);
        $duration = ($end - $start) * 1000; // en millisecondes

        $this->assertLessThan(100, $duration, "Creating $iterations tasks should take less than 100ms");
    }

    public function testProjectCreationPerformance(): void
    {
        $iterations = 500;
        $start = microtime(true);

        for ($i = 0; $i < $iterations; ++$i) {
            $project = new Project();
            $project->setName("Project $i");
            $project->setDescription("Description for project $i");
        }

        $end = microtime(true);
        $duration = ($end - $start) * 1000; // en millisecondes

        $this->assertLessThan(50, $duration, "Creating $iterations projects should take less than 50ms");
    }

    public function testUserCreationPerformance(): void
    {
        $iterations = 500;
        $start = microtime(true);

        for ($i = 0; $i < $iterations; ++$i) {
            $user = new User();
            $user->setEmail("user$<EMAIL>");
            $user->setFirstName('User');
            $user->setLastName("$i");
            $user->setRoles(['ROLE_USER']);
        }

        $end = microtime(true);
        $duration = ($end - $start) * 1000; // en millisecondes

        $this->assertLessThan(50, $duration, "Creating $iterations users should take less than 50ms");
    }

    public function testCO2CalculationPerformance(): void
    {
        $task = new Task();
        $task->setEstimatedHours(10);
        $task->setActualHours(12);
        $task->setType(Task::TYPE_TECHNICAL);

        $iterations = 10000;
        $start = microtime(true);

        for ($i = 0; $i < $iterations; ++$i) {
            $co2 = $task->getCO2Emission();
        }

        $end = microtime(true);
        $duration = ($end - $start) * 1000; // en millisecondes

        $this->assertLessThan(100, $duration, "Calculating CO2 $iterations times should take less than 100ms");
    }

    public function testMemoryUsage(): void
    {
        $initialMemory = memory_get_usage();

        $tasks = [];

        for ($i = 0; $i < 1000; ++$i) {
            $task = new Task();
            $task->setTitle("Task $i");
            $task->setDescription("Description for task $i");
            $tasks[] = $task;
        }

        $finalMemory = memory_get_usage();
        $memoryUsed = $finalMemory - $initialMemory;

        // Moins de 5MB pour 1000 tâches
        $this->assertLessThan(5 * 1024 * 1024, $memoryUsed, 'Memory usage should be less than 5MB for 1000 tasks');

        unset($tasks);
    }

    public function testLargeProjectPerformance(): void
    {
        $project = new Project();
        $project->setName('Large Project');

        $start = microtime(true);

        // Ajouter 100 tâches au projet
        for ($i = 0; $i < 100; ++$i) {
            $task = new Task();
            $task->setTitle("Task $i");
            $task->setEstimatedHours(rand(1, 10));
            $task->setActualHours(rand(1, 12));
            $task->setType(Task::TYPE_TECHNICAL);
            $project->addTask($task);
        }

        // Calculer les émissions CO2 totales
        $totalCO2 = $project->getTotalCO2Emissions();

        $end = microtime(true);
        $duration = ($end - $start) * 1000; // en millisecondes

        $this->assertLessThan(50, $duration, 'Processing large project should take less than 50ms');
        $this->assertGreaterThan(0, $totalCO2, 'Total CO2 should be greater than 0');
    }
}
