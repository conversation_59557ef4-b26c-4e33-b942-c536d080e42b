<?php

namespace App\Tests\Repository;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use App\Repository\ProjectRepository;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ProjectRepositoryTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;

    private ProjectRepository $projectRepository;

    protected function setUp(): void
    {
        $kernel = self::bootKernel();
        $this->entityManager = $kernel->getContainer()
            ->get('doctrine')
            ->getManager();

        $this->projectRepository = $this->entityManager->getRepository(Project::class);
    }

    protected function tearDown(): void
    {
        $this->entityManager->createQuery('DELETE FROM App\Entity\Task')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\Project')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\User')->execute();
        parent::tearDown();
    }

    public function testFindAllProjects(): void
    {
        $this->createTestProject('Project 1');
        $this->createTestProject('Project 2');

        $projects = $this->projectRepository->findAll();

        $this->assertCount(2, $projects);
    }

    public function testFindProjectByName(): void
    {
        $project = $this->createTestProject('Unique Project');

        $foundProject = $this->projectRepository->findOneBy(['name' => 'Unique Project']);

        $this->assertNotNull($foundProject);
        $this->assertEquals('Unique Project', $foundProject->getName());
    }

    public function testFindProjectsOrderedByCreatedAt(): void
    {
        $project1 = $this->createTestProject('First Project');
        sleep(1); // Assurer une différence de temps
        $project2 = $this->createTestProject('Second Project');

        $projects = $this->projectRepository->findBy([], ['createdAt' => 'DESC']);

        $this->assertEquals('Second Project', $projects[0]->getName());
        $this->assertEquals('First Project', $projects[1]->getName());
    }

    public function testCountProjects(): void
    {
        $this->createTestProject('Project 1');
        $this->createTestProject('Project 2');
        $this->createTestProject('Project 3');

        $count = $this->projectRepository->count([]);

        $this->assertEquals(3, $count);
    }

    public function testFindProjectsWithTasks(): void
    {
        $projectWithTasks = $this->createTestProject('Project With Tasks');
        $projectWithoutTasks = $this->createTestProject('Project Without Tasks');

        // Ajouter des tâches au premier projet
        $task1 = $this->createTestTask('Task 1');
        $task1->setProject($projectWithTasks);

        $task2 = $this->createTestTask('Task 2');
        $task2->setProject($projectWithTasks);

        $this->entityManager->flush();

        // Rafraîchir les entités
        $this->entityManager->refresh($projectWithTasks);
        $this->entityManager->refresh($projectWithoutTasks);

        $this->assertCount(2, $projectWithTasks->getTasks());
        $this->assertCount(0, $projectWithoutTasks->getTasks());
    }

    public function testFindProjectsWithMembers(): void
    {
        $project = $this->createTestProject('Project With Members');

        $user1 = $this->createTestUser('<EMAIL>');
        $user2 = $this->createTestUser('<EMAIL>');

        $project->addMember($user1);
        $project->addMember($user2);

        $this->entityManager->flush();
        $this->entityManager->refresh($project);

        $this->assertCount(2, $project->getMembers());
        $this->assertTrue($project->getMembers()->contains($user1));
        $this->assertTrue($project->getMembers()->contains($user2));
    }

    public function testProjectDeleteWithTasks(): void
    {
        $project = $this->createTestProject('Project To Delete');

        $task1 = $this->createTestTask('Task 1');
        $task1->setProject($project);

        $task2 = $this->createTestTask('Task 2');
        $task2->setProject($project);

        $this->entityManager->flush();

        $projectId = $project->getId();
        $task1Id = $task1->getId();
        $task2Id = $task2->getId();

        // Supprimer d'abord les tâches puis le projet
        $this->entityManager->remove($task1);
        $this->entityManager->remove($task2);
        $this->entityManager->remove($project);
        $this->entityManager->flush();

        // Vérifier que le projet et ses tâches ont été supprimés
        $deletedProject = $this->projectRepository->find($projectId);
        $deletedTask1 = $this->entityManager->getRepository(Task::class)->find($task1Id);
        $deletedTask2 = $this->entityManager->getRepository(Task::class)->find($task2Id);

        $this->assertNull($deletedProject);
        $this->assertNull($deletedTask1);
        $this->assertNull($deletedTask2);
    }

    public function testFindProjectsWithCo2Emissions(): void
    {
        $project = $this->createTestProject('CO2 Project');

        $task1 = $this->createTestTask('Office Task');
        $task1->setType(Task::TYPE_OFFICE_LIGHT);
        $task1->setActualHours('2.0');
        $task1->setProject($project);

        $task2 = $this->createTestTask('Technical Task');
        $task2->setType(Task::TYPE_TECHNICAL);
        $task2->setActualHours('4.0');
        $task2->setProject($project);

        $this->entityManager->flush();
        $this->entityManager->refresh($project);

        $expectedCo2 =
            (2.0 * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT]) +
            (4.0 * Task::CO2_RATES[Task::TYPE_TECHNICAL]);

        $this->assertEquals($expectedCo2, $project->getTotalCo2Emissions());
    }

    public function testFindRecentProjects(): void
    {
        $oldProject = $this->createTestProject('Old Project');
        $oldProject->setCreatedAt(new DateTimeImmutable('-1 month'));

        $recentProject = $this->createTestProject('Recent Project');
        $recentProject->setCreatedAt(new DateTimeImmutable('-1 day'));

        $this->entityManager->flush();

        // Trouver les projets récents (derniers 7 jours)
        $recentProjects = $this->projectRepository->createQueryBuilder('p')
            ->where('p.createdAt >= :date')
            ->setParameter('date', new DateTimeImmutable('-7 days'))
            ->getQuery()
            ->getResult();

        $this->assertCount(1, $recentProjects);
        $this->assertEquals('Recent Project', $recentProjects[0]->getName());
    }

    public function testFindProjectsUpdatedRecently(): void
    {
        $project1 = $this->createTestProject('Project 1');
        $project2 = $this->createTestProject('Project 2');

        // Mettre à jour seulement le projet 2
        $project2->setUpdatedAt(new DateTimeImmutable());
        $this->entityManager->flush();

        $updatedProjects = $this->projectRepository->createQueryBuilder('p')
            ->where('p.updatedAt IS NOT NULL')
            ->getQuery()
            ->getResult();

        $this->assertCount(1, $updatedProjects);
        $this->assertEquals('Project 2', $updatedProjects[0]->getName());
    }

    private function createTestProject(string $name = 'Test Project'): Project
    {
        $project = new Project();
        $project->setName($name);
        $project->setDescription('Test Description');

        $this->entityManager->persist($project);
        $this->entityManager->flush();

        return $project;
    }

    private function createTestTask(string $title = 'Test Task'): Task
    {
        $project = $this->createTestProject();

        $task = new Task();
        $task->setTitle($title);
        $task->setProject($project);

        $this->entityManager->persist($task);
        $this->entityManager->flush();

        return $task;
    }

    private function createTestUser(string $email = '<EMAIL>'): User
    {
        $user = new User();
        $user->setEmail($email);
        $user->setFirstName('Test');
        $user->setLastName('User');
        $user->setPassword('password');

        $this->entityManager->persist($user);
        $this->entityManager->flush();

        return $user;
    }
}
