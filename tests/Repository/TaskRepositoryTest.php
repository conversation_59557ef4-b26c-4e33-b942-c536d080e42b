<?php

namespace App\Tests\Repository;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use App\Repository\TaskRepository;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class TaskRepositoryTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;
    private TaskRepository $taskRepository;

    protected function setUp(): void
    {
        $kernel = self::bootKernel();
        $this->entityManager = $kernel->getContainer()
            ->get('doctrine')
            ->getManager();
        
        $this->taskRepository = $this->entityManager->getRepository(Task::class);
    }

    protected function tearDown(): void
    {
        $this->entityManager->createQuery('DELETE FROM App\Entity\Task')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\Project')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\User')->execute();
        parent::tearDown();
    }

    public function testFindAllTasks(): void
    {
        $this->createTestTask('Task 1');
        $this->createTestTask('Task 2');

        $tasks = $this->taskRepository->findAll();

        $this->assertCount(2, $tasks);
    }

    public function testFindTasksByStatus(): void
    {
        $this->createTestTask('Todo Task', Task::STATUS_TODO);
        $this->createTestTask('In Progress Task', Task::STATUS_IN_PROGRESS);
        $this->createTestTask('Done Task', Task::STATUS_DONE);

        $todoTasks = $this->taskRepository->findBy(['status' => Task::STATUS_TODO]);
        $inProgressTasks = $this->taskRepository->findBy(['status' => Task::STATUS_IN_PROGRESS]);
        $doneTasks = $this->taskRepository->findBy(['status' => Task::STATUS_DONE]);

        $this->assertCount(1, $todoTasks);
        $this->assertCount(1, $inProgressTasks);
        $this->assertCount(1, $doneTasks);
    }

    public function testFindTasksByPriority(): void
    {
        $this->createTestTask('Low Priority', Task::STATUS_TODO, Task::PRIORITY_LOW);
        $this->createTestTask('High Priority', Task::STATUS_TODO, Task::PRIORITY_HIGH);
        $this->createTestTask('Urgent Priority', Task::STATUS_TODO, Task::PRIORITY_URGENT);

        $highPriorityTasks = $this->taskRepository->findBy(['priority' => Task::PRIORITY_HIGH]);
        $urgentTasks = $this->taskRepository->findBy(['priority' => Task::PRIORITY_URGENT]);

        $this->assertCount(1, $highPriorityTasks);
        $this->assertCount(1, $urgentTasks);
    }

    public function testFindTasksByType(): void
    {
        $this->createTestTask('Office Task', Task::STATUS_TODO, Task::PRIORITY_MEDIUM, Task::TYPE_OFFICE_LIGHT);
        $this->createTestTask('Technical Task', Task::STATUS_TODO, Task::PRIORITY_MEDIUM, Task::TYPE_TECHNICAL);
        $this->createTestTask('Energy Task', Task::STATUS_TODO, Task::PRIORITY_MEDIUM, Task::TYPE_ENERGY_INTENSIVE);

        $technicalTasks = $this->taskRepository->findBy(['type' => Task::TYPE_TECHNICAL]);
        $energyTasks = $this->taskRepository->findBy(['type' => Task::TYPE_ENERGY_INTENSIVE]);

        $this->assertCount(1, $technicalTasks);
        $this->assertCount(1, $energyTasks);
    }

    public function testFindTasksOrderedByCreatedAt(): void
    {
        $task1 = $this->createTestTask('First Task');
        sleep(1); // Assurer une différence de temps
        $task2 = $this->createTestTask('Second Task');

        $tasks = $this->taskRepository->findBy([], ['createdAt' => 'DESC']);

        $this->assertEquals('Second Task', $tasks[0]->getTitle());
        $this->assertEquals('First Task', $tasks[1]->getTitle());
    }

    public function testCountTasksByStatus(): void
    {
        $this->createTestTask('Todo 1', Task::STATUS_TODO);
        $this->createTestTask('Todo 2', Task::STATUS_TODO);
        $this->createTestTask('In Progress', Task::STATUS_IN_PROGRESS);
        $this->createTestTask('Done', Task::STATUS_DONE);

        $todoCount = $this->taskRepository->count(['status' => Task::STATUS_TODO]);
        $inProgressCount = $this->taskRepository->count(['status' => Task::STATUS_IN_PROGRESS]);
        $doneCount = $this->taskRepository->count(['status' => Task::STATUS_DONE]);

        $this->assertEquals(2, $todoCount);
        $this->assertEquals(1, $inProgressCount);
        $this->assertEquals(1, $doneCount);
    }

    public function testFindOverdueTasks(): void
    {
        // Tâche en retard
        $overdueTask = $this->createTestTask('Overdue Task', Task::STATUS_TODO);
        $overdueTask->setDueDate(new DateTimeImmutable('-1 day'));
        $this->entityManager->flush();

        // Tâche à venir
        $futureTask = $this->createTestTask('Future Task', Task::STATUS_TODO);
        $futureTask->setDueDate(new DateTimeImmutable('+1 day'));
        $this->entityManager->flush();

        // Tâche terminée en retard (ne devrait pas être considérée comme en retard)
        $completedOverdueTask = $this->createTestTask('Completed Overdue', Task::STATUS_DONE);
        $completedOverdueTask->setDueDate(new DateTimeImmutable('-1 day'));
        $this->entityManager->flush();

        // Utiliser une requête personnalisée pour trouver les tâches en retard
        $overdueTasks = $this->taskRepository->createQueryBuilder('t')
            ->where('t.dueDate < :now')
            ->andWhere('t.status != :done')
            ->setParameter('now', new DateTimeImmutable())
            ->setParameter('done', Task::STATUS_DONE)
            ->getQuery()
            ->getResult();

        $this->assertCount(1, $overdueTasks);
        $this->assertEquals('Overdue Task', $overdueTasks[0]->getTitle());
    }

    public function testFindTasksByProject(): void
    {
        $project1 = $this->createTestProject('Project 1');
        $project2 = $this->createTestProject('Project 2');

        $task1 = $this->createTestTask('Task 1');
        $task1->setProject($project1);

        $task2 = $this->createTestTask('Task 2');
        $task2->setProject($project1);

        $task3 = $this->createTestTask('Task 3');
        $task3->setProject($project2);

        $this->entityManager->flush();

        $project1Tasks = $this->taskRepository->findBy(['project' => $project1]);
        $project2Tasks = $this->taskRepository->findBy(['project' => $project2]);

        $this->assertCount(2, $project1Tasks);
        $this->assertCount(1, $project2Tasks);
    }

    public function testFindTasksByAssignedUser(): void
    {
        $user1 = $this->createTestUser('<EMAIL>');
        $user2 = $this->createTestUser('<EMAIL>');

        $task1 = $this->createTestTask('Task 1');
        $task1->setAssignedTo($user1);

        $task2 = $this->createTestTask('Task 2');
        $task2->setAssignedTo($user1);

        $task3 = $this->createTestTask('Task 3');
        $task3->setAssignedTo($user2);

        $this->entityManager->flush();

        $user1Tasks = $this->taskRepository->findBy(['assignedTo' => $user1]);
        $user2Tasks = $this->taskRepository->findBy(['assignedTo' => $user2]);

        $this->assertCount(2, $user1Tasks);
        $this->assertCount(1, $user2Tasks);
    }

    private function createTestTask(
        string $title = 'Test Task',
        string $status = Task::STATUS_TODO,
        string $priority = Task::PRIORITY_MEDIUM,
        string $type = Task::TYPE_OFFICE_LIGHT
    ): Task {
        $task = new Task();
        $task->setTitle($title);
        $task->setStatus($status);
        $task->setPriority($priority);
        $task->setType($type);

        $this->entityManager->persist($task);
        $this->entityManager->flush();

        return $task;
    }

    private function createTestProject(string $name = 'Test Project'): Project
    {
        $project = new Project();
        $project->setName($name);

        $this->entityManager->persist($project);
        $this->entityManager->flush();

        return $project;
    }

    private function createTestUser(string $email = '<EMAIL>'): User
    {
        $user = new User();
        $user->setEmail($email);
        $user->setFirstName('Test');
        $user->setLastName('User');
        $user->setPassword('password');

        $this->entityManager->persist($user);
        $this->entityManager->flush();

        return $user;
    }
}
