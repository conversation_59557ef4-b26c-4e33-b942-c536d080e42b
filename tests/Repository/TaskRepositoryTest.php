<?php

namespace App\Tests\Repository;

use App\Entity\Project;
use App\Entity\Task;
use App\Repository\TaskRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class TaskRepositoryTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;

    private TaskRepository $taskRepository;

    protected function setUp(): void
    {
        $kernel = self::bootKernel();
        $this->entityManager = $kernel->getContainer()->get('doctrine')->getManager();
        $this->taskRepository = $this->entityManager->getRepository(Task::class);

        // Clean up the database
        $this->entityManager->createQuery('DELETE FROM App\Entity\Task')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\Project')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\User')->execute();
    }

    public function testFindAll(): void
    {
        // Initially empty
        $tasks = $this->taskRepository->findAll();
        $this->assertCount(0, $tasks);

        // Create test project
        $project = new Project();
        $project->setName('Test Project');
        $project->setDescription('Test Description');

        // Create test task
        $task = new Task();
        $task->setTitle('Test Task');
        $task->setDescription('Test Description');
        $task->setStatus(Task::STATUS_TODO);
        $task->setPriority(Task::PRIORITY_MEDIUM);
        $task->setType(Task::TYPE_TECHNICAL);
        $task->setProject($project);

        $this->entityManager->persist($project);
        $this->entityManager->persist($task);
        $this->entityManager->flush();

        $tasks = $this->taskRepository->findAll();
        $this->assertCount(1, $tasks);
        $this->assertEquals('Test Task', $tasks[0]->getTitle());
    }

    public function testFindByStatus(): void
    {
        // Create test project
        $project = new Project();
        $project->setName('Status Test Project');
        $project->setDescription('Project for status testing');

        // Create tasks with different statuses
        $todoTask = new Task();
        $todoTask->setTitle('Todo Task');
        $todoTask->setStatus(Task::STATUS_TODO);
        $todoTask->setPriority(Task::PRIORITY_MEDIUM);
        $todoTask->setType(Task::TYPE_TECHNICAL);
        $todoTask->setProject($project);

        $doneTask = new Task();
        $doneTask->setTitle('Done Task');
        $doneTask->setStatus(Task::STATUS_DONE);
        $doneTask->setPriority(Task::PRIORITY_HIGH);
        $doneTask->setType(Task::TYPE_OFFICE_LIGHT);
        $doneTask->setProject($project);

        $this->entityManager->persist($project);
        $this->entityManager->persist($todoTask);
        $this->entityManager->persist($doneTask);
        $this->entityManager->flush();

        $todoTasks = $this->taskRepository->findBy(['status' => Task::STATUS_TODO]);
        $doneTasks = $this->taskRepository->findBy(['status' => Task::STATUS_DONE]);

        $this->assertCount(1, $todoTasks);
        $this->assertCount(1, $doneTasks);
        $this->assertEquals('Todo Task', $todoTasks[0]->getTitle());
        $this->assertEquals('Done Task', $doneTasks[0]->getTitle());
    }

    public function testFindByPriority(): void
    {
        // Create test project
        $project = new Project();
        $project->setName('Priority Test Project');
        $project->setDescription('Project for priority testing');

        // Create tasks with different priorities
        $highTask = new Task();
        $highTask->setTitle('High Priority Task');
        $highTask->setStatus(Task::STATUS_TODO);
        $highTask->setPriority(Task::PRIORITY_HIGH);
        $highTask->setType(Task::TYPE_TECHNICAL);
        $highTask->setProject($project);

        $lowTask = new Task();
        $lowTask->setTitle('Low Priority Task');
        $lowTask->setStatus(Task::STATUS_TODO);
        $lowTask->setPriority(Task::PRIORITY_LOW);
        $lowTask->setType(Task::TYPE_OFFICE_LIGHT);
        $lowTask->setProject($project);

        $this->entityManager->persist($project);
        $this->entityManager->persist($highTask);
        $this->entityManager->persist($lowTask);
        $this->entityManager->flush();

        $highTasks = $this->taskRepository->findBy(['priority' => Task::PRIORITY_HIGH]);
        $lowTasks = $this->taskRepository->findBy(['priority' => Task::PRIORITY_LOW]);

        $this->assertCount(1, $highTasks);
        $this->assertCount(1, $lowTasks);
        $this->assertEquals('High Priority Task', $highTasks[0]->getTitle());
        $this->assertEquals('Low Priority Task', $lowTasks[0]->getTitle());
    }

    public function testFindByType(): void
    {
        // Create test project
        $project = new Project();
        $project->setName('Type Test Project');
        $project->setDescription('Project for type testing');

        // Create tasks with different types
        $technicalTask = new Task();
        $technicalTask->setTitle('Technical Task');
        $technicalTask->setStatus(Task::STATUS_TODO);
        $technicalTask->setPriority(Task::PRIORITY_MEDIUM);
        $technicalTask->setType(Task::TYPE_TECHNICAL);
        $technicalTask->setProject($project);

        $officeTask = new Task();
        $officeTask->setTitle('Office Task');
        $officeTask->setStatus(Task::STATUS_TODO);
        $officeTask->setPriority(Task::PRIORITY_MEDIUM);
        $officeTask->setType(Task::TYPE_OFFICE_LIGHT);
        $officeTask->setProject($project);

        $this->entityManager->persist($project);
        $this->entityManager->persist($technicalTask);
        $this->entityManager->persist($officeTask);
        $this->entityManager->flush();

        $technicalTasks = $this->taskRepository->findBy(['type' => Task::TYPE_TECHNICAL]);
        $officeTasks = $this->taskRepository->findBy(['type' => Task::TYPE_OFFICE_LIGHT]);

        $this->assertCount(1, $technicalTasks);
        $this->assertCount(1, $officeTasks);
        $this->assertEquals('Technical Task', $technicalTasks[0]->getTitle());
        $this->assertEquals('Office Task', $officeTasks[0]->getTitle());
    }

    public function testCount(): void
    {
        $this->assertEquals(0, $this->taskRepository->count([]));

        // Create test project and task
        $project = new Project();
        $project->setName('Count Test Project');
        $project->setDescription('Project for count testing');

        $task = new Task();
        $task->setTitle('Count Task');
        $task->setStatus(Task::STATUS_TODO);
        $task->setPriority(Task::PRIORITY_MEDIUM);
        $task->setType(Task::TYPE_TECHNICAL);
        $task->setProject($project);

        $this->entityManager->persist($project);
        $this->entityManager->persist($task);
        $this->entityManager->flush();

        $this->assertEquals(1, $this->taskRepository->count([]));
        $this->assertEquals(1, $this->taskRepository->count(['status' => Task::STATUS_TODO]));
        $this->assertEquals(0, $this->taskRepository->count(['status' => Task::STATUS_DONE]));
    }

    public function testFindOneBy(): void
    {
        // Create test project
        $project = new Project();
        $project->setName('FindOne Test Project');
        $project->setDescription('Project for findOne testing');

        $task = new Task();
        $task->setTitle('Unique Task');
        $task->setStatus(Task::STATUS_TODO);
        $task->setPriority(Task::PRIORITY_URGENT);
        $task->setType(Task::TYPE_ENERGY_INTENSIVE);
        $task->setProject($project);

        $this->entityManager->persist($project);
        $this->entityManager->persist($task);
        $this->entityManager->flush();

        $foundTask = $this->taskRepository->findOneBy(['title' => 'Unique Task']);
        $this->assertNotNull($foundTask);
        $this->assertEquals('Unique Task', $foundTask->getTitle());
        $this->assertEquals(Task::PRIORITY_URGENT, $foundTask->getPriority());

        $notFoundTask = $this->taskRepository->findOneBy(['title' => 'Non-existent Task']);
        $this->assertNull($notFoundTask);
    }

    public function testFindByProject(): void
    {
        // Create test projects
        $project1 = new Project();
        $project1->setName('Project 1');
        $project1->setDescription('First project');

        $project2 = new Project();
        $project2->setName('Project 2');
        $project2->setDescription('Second project');

        // Create tasks for different projects
        $task1 = new Task();
        $task1->setTitle('Task for Project 1');
        $task1->setStatus(Task::STATUS_TODO);
        $task1->setPriority(Task::PRIORITY_MEDIUM);
        $task1->setType(Task::TYPE_TECHNICAL);
        $task1->setProject($project1);

        $task2 = new Task();
        $task2->setTitle('Task for Project 2');
        $task2->setStatus(Task::STATUS_TODO);
        $task2->setPriority(Task::PRIORITY_MEDIUM);
        $task2->setType(Task::TYPE_OFFICE_LIGHT);
        $task2->setProject($project2);

        $this->entityManager->persist($project1);
        $this->entityManager->persist($project2);
        $this->entityManager->persist($task1);
        $this->entityManager->persist($task2);
        $this->entityManager->flush();

        $project1Tasks = $this->taskRepository->findBy(['project' => $project1]);
        $project2Tasks = $this->taskRepository->findBy(['project' => $project2]);

        $this->assertCount(1, $project1Tasks);
        $this->assertCount(1, $project2Tasks);
        $this->assertEquals('Task for Project 1', $project1Tasks[0]->getTitle());
        $this->assertEquals('Task for Project 2', $project2Tasks[0]->getTitle());
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->entityManager->close();
    }
}
