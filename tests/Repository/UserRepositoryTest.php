<?php

namespace App\Tests\Repository;

use App\Entity\User;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;

class UserRepositoryTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;
    private UserRepository $userRepository;

    protected function setUp(): void
    {
        $kernel = self::bootKernel();
        $this->entityManager = $kernel->getContainer()
            ->get('doctrine')
            ->getManager();

        $this->userRepository = $this->entityManager->getRepository(User::class);
    }

    protected function tearDown(): void
    {
        // Supprimer d'abord les tâches puis les utilisateurs à cause des contraintes de clé étrangère
        $this->entityManager->createQuery('DELETE FROM App\Entity\Task')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\Project')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\User')->execute();
        parent::tearDown();
    }

    public function testFindUserByEmail(): void
    {
        $email = '<EMAIL>';
        $user = $this->createTestUser($email);

        $foundUser = $this->userRepository->findOneBy(['email' => $email]);

        $this->assertNotNull($foundUser);
        $this->assertEquals($email, $foundUser->getEmail());
        $this->assertEquals('John', $foundUser->getFirstName());
        $this->assertEquals('Doe', $foundUser->getLastName());
    }

    public function testFindAllUsers(): void
    {
        $this->createTestUser();
        $this->createTestUser('<EMAIL>', 'Jane', 'Smith');

        $users = $this->userRepository->findAll();

        $this->assertCount(2, $users);
    }

    public function testUpgradePassword(): void
    {
        $user = $this->createTestUser();
        $newPassword = 'new_hashed_password';

        $this->userRepository->upgradePassword($user, $newPassword);

        $this->assertEquals($newPassword, $user->getPassword());

        // Vérifier que l'utilisateur a été persisté
        $this->entityManager->refresh($user);
        $this->assertEquals($newPassword, $user->getPassword());
    }

    public function testUpgradePasswordWithInvalidUser(): void
    {
        $invalidUser = new class implements \Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface {
            public function getPassword(): ?string
            {
                return 'password';
            }
        };

        $this->expectException(UnsupportedUserException::class);
        $this->expectExceptionMessage('Instances of');

        $this->userRepository->upgradePassword($invalidUser, 'new_password');
    }

    public function testFindUsersByRole(): void
    {
        $adminUser = $this->createTestUser('<EMAIL>', 'Admin', 'User');
        $adminUser->setRoles(['ROLE_ADMIN']);
        $this->entityManager->flush();

        $regularUser = $this->createTestUser('<EMAIL>', 'Regular', 'User');

        // Test de recherche par rôle (nécessiterait une méthode personnalisée)
        $allUsers = $this->userRepository->findAll();
        $adminUsers = array_filter($allUsers, fn($user) => in_array('ROLE_ADMIN', $user->getRoles()));

        $this->assertCount(1, $adminUsers);
        $this->assertEquals('<EMAIL>', array_values($adminUsers)[0]->getEmail());
    }

    public function testCountUsers(): void
    {
        $this->createTestUser();
        $this->createTestUser('<EMAIL>', 'User', 'Two');

        $count = $this->userRepository->count([]);

        $this->assertEquals(2, $count);
    }

    public function testFindUsersWithTasks(): void
    {
        $user1 = $this->createTestUser();
        $user2 = $this->createTestUser('<EMAIL>', 'User', 'Two');

        // Créer un projet et une tâche pour user1
        $project = new \App\Entity\Project();
        $project->setName('Test Project');
        $this->entityManager->persist($project);

        $task = new \App\Entity\Task();
        $task->setTitle('Test Task');
        $task->setProject($project);
        $task->setAssignedTo($user1);
        $this->entityManager->persist($task);
        $this->entityManager->flush();

        // Vérifier que user1 a des tâches et user2 n'en a pas
        $this->entityManager->refresh($user1);
        $this->entityManager->refresh($user2);

        $this->assertCount(1, $user1->getTasks());
        $this->assertCount(0, $user2->getTasks());
    }

    public function testUniqueEmailConstraint(): void
    {
        $this->createTestUser('<EMAIL>');

        // Essayer de créer un autre utilisateur avec le même email
        $duplicateUser = new User();
        $duplicateUser->setEmail('<EMAIL>');
        $duplicateUser->setFirstName('Duplicate');
        $duplicateUser->setLastName('User');
        $duplicateUser->setPassword('password');

        $this->entityManager->persist($duplicateUser);

        $this->expectException(\Doctrine\DBAL\Exception\UniqueConstraintViolationException::class);
        $this->entityManager->flush();
    }

    private function createTestUser(
        string $email = null,
        string $firstName = 'John',
        string $lastName = 'Doe'
    ): User {
        if ($email === null) {
            $email = 'test' . uniqid() . '@example.com';
        }

        $user = new User();
        $user->setEmail($email);
        $user->setFirstName($firstName);
        $user->setLastName($lastName);
        $user->setPassword('hashed_password');

        $this->entityManager->persist($user);
        $this->entityManager->flush();

        return $user;
    }
}
