<?php

namespace App\Tests\Service;

use App\Entity\Project;
use App\Entity\Task;
use App\Service\Co2CalculatorService;
use PHPUnit\Framework\TestCase;

class Co2CalculatorServiceTest extends TestCase
{
    private Co2CalculatorService $co2Calculator;

    protected function setUp(): void
    {
        $this->co2Calculator = new Co2CalculatorService();
    }

    public function testCalculateTaskCo2WithActualHours(): void
    {
        $task = new Task();
        $task->setType(Task::TYPE_TECHNICAL);
        $task->setActualHours('5.0');
        $task->setEstimatedHours('3.0'); // Should be ignored

        $expectedCo2 = 5.0 * Task::CO2_RATES[Task::TYPE_TECHNICAL];
        $actualCo2 = $this->co2Calculator->calculateTaskCo2($task);

        $this->assertEquals($expectedCo2, $actualCo2);
    }

    public function testCalculateTaskCo2WithEstimatedHours(): void
    {
        $task = new Task();
        $task->setType(Task::TYPE_OFFICE_LIGHT);
        $task->setEstimatedHours('4.0');

        $expectedCo2 = 4.0 * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT];
        $actualCo2 = $this->co2Calculator->calculateTaskCo2($task);

        $this->assertEquals($expectedCo2, $actualCo2);
    }

    public function testCalculateTaskCo2WithNoHours(): void
    {
        $task = new Task();
        $task->setType(Task::TYPE_ENERGY_INTENSIVE);

        $actualCo2 = $this->co2Calculator->calculateTaskCo2($task);

        $this->assertEquals(0.0, $actualCo2);
    }

    public function testGetCo2RateForTaskType(): void
    {
        $this->assertEquals(
            Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT],
            $this->co2Calculator->getCo2RateForTaskType(Task::TYPE_OFFICE_LIGHT),
        );

        $this->assertEquals(
            Task::CO2_RATES[Task::TYPE_TECHNICAL],
            $this->co2Calculator->getCo2RateForTaskType(Task::TYPE_TECHNICAL),
        );

        $this->assertEquals(
            Task::CO2_RATES[Task::TYPE_ENERGY_INTENSIVE],
            $this->co2Calculator->getCo2RateForTaskType(Task::TYPE_ENERGY_INTENSIVE),
        );
    }

    public function testGetCo2RateForUnknownTaskType(): void
    {
        $rate = $this->co2Calculator->getCo2RateForTaskType('unknown_type');

        $this->assertEquals(Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT], $rate);
    }

    public function testCalculateProjectCo2(): void
    {
        $project = new Project();

        $task1 = new Task();
        $task1->setType(Task::TYPE_OFFICE_LIGHT);
        $task1->setActualHours('2.0');
        $project->addTask($task1);

        $task2 = new Task();
        $task2->setType(Task::TYPE_TECHNICAL);
        $task2->setActualHours('3.0');
        $project->addTask($task2);

        $expectedCo2 =
            (2.0 * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT]) +
            (3.0 * Task::CO2_RATES[Task::TYPE_TECHNICAL]);

        $actualCo2 = $this->co2Calculator->calculateProjectCo2($project);

        $this->assertEquals($expectedCo2, $actualCo2);
    }

    public function testCalculateProjectCo2WithEmptyProject(): void
    {
        $project = new Project();

        $actualCo2 = $this->co2Calculator->calculateProjectCo2($project);

        $this->assertEquals(0.0, $actualCo2);
    }

    public function testCalculateCo2ByType(): void
    {
        $project = new Project();

        $task1 = new Task();
        $task1->setType(Task::TYPE_OFFICE_LIGHT);
        $task1->setActualHours('2.0');
        $project->addTask($task1);

        $task2 = new Task();
        $task2->setType(Task::TYPE_OFFICE_LIGHT);
        $task2->setActualHours('1.0');
        $project->addTask($task2);

        $task3 = new Task();
        $task3->setType(Task::TYPE_TECHNICAL);
        $task3->setActualHours('3.0');
        $project->addTask($task3);

        $co2ByType = $this->co2Calculator->calculateCo2ByType($project);

        $expectedOfficeCo2 = (2.0 + 1.0) * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT];
        $expectedTechnicalCo2 = 3.0 * Task::CO2_RATES[Task::TYPE_TECHNICAL];

        $this->assertEquals($expectedOfficeCo2, $co2ByType[Task::TYPE_OFFICE_LIGHT]);
        $this->assertEquals($expectedTechnicalCo2, $co2ByType[Task::TYPE_TECHNICAL]);
        $this->assertEquals(0.0, $co2ByType[Task::TYPE_ENERGY_INTENSIVE]);
    }

    public function testCalculateAverageCo2PerHour(): void
    {
        $project = new Project();

        $task1 = new Task();
        $task1->setType(Task::TYPE_OFFICE_LIGHT);
        $task1->setActualHours('2.0');
        $project->addTask($task1);

        $task2 = new Task();
        $task2->setType(Task::TYPE_TECHNICAL);
        $task2->setActualHours('4.0');
        $project->addTask($task2);

        $totalHours = 2.0 + 4.0;
        $totalCo2 =
            (2.0 * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT]) +
            (4.0 * Task::CO2_RATES[Task::TYPE_TECHNICAL]);
        $expectedAverage = $totalCo2 / $totalHours;

        $actualAverage = $this->co2Calculator->calculateAverageCo2PerHour($project);

        $this->assertEquals($expectedAverage, $actualAverage);
    }

    public function testCalculateAverageCo2PerHourWithNoHours(): void
    {
        $project = new Project();

        $task = new Task();
        $task->setType(Task::TYPE_OFFICE_LIGHT);
        // Pas d'heures définies
        $project->addTask($task);

        $actualAverage = $this->co2Calculator->calculateAverageCo2PerHour($project);

        $this->assertEquals(0.0, $actualAverage);
    }

    public function testEstimateFutureCo2(): void
    {
        $project = new Project();

        // Tâche terminée - ne devrait pas être incluse
        $completedTask = new Task();
        $completedTask->setType(Task::TYPE_TECHNICAL);
        $completedTask->setStatus(Task::STATUS_DONE);
        $completedTask->setEstimatedHours('5.0');
        $project->addTask($completedTask);

        // Tâche en cours - devrait être incluse
        $inProgressTask = new Task();
        $inProgressTask->setType(Task::TYPE_OFFICE_LIGHT);
        $inProgressTask->setStatus(Task::STATUS_IN_PROGRESS);
        $inProgressTask->setEstimatedHours('3.0');
        $project->addTask($inProgressTask);

        // Tâche à faire - devrait être incluse
        $todoTask = new Task();
        $todoTask->setType(Task::TYPE_ENERGY_INTENSIVE);
        $todoTask->setStatus(Task::STATUS_TODO);
        $todoTask->setEstimatedHours('2.0');
        $project->addTask($todoTask);

        $expectedFutureCo2 =
            (3.0 * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT]) +
            (2.0 * Task::CO2_RATES[Task::TYPE_ENERGY_INTENSIVE]);

        $actualFutureCo2 = $this->co2Calculator->estimateFutureCo2($project);

        $this->assertEquals($expectedFutureCo2, $actualFutureCo2);
    }

    public function testCompareActualVsEstimated(): void
    {
        $project = new Project();

        $task1 = new Task();
        $task1->setType(Task::TYPE_OFFICE_LIGHT);
        $task1->setActualHours('3.0');
        $task1->setEstimatedHours('2.0');
        $project->addTask($task1);

        $task2 = new Task();
        $task2->setType(Task::TYPE_TECHNICAL);
        $task2->setActualHours('4.0');
        $task2->setEstimatedHours('5.0');
        $project->addTask($task2);

        $comparison = $this->co2Calculator->compareActualVsEstimated($project);

        $expectedActual =
            (3.0 * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT]) +
            (4.0 * Task::CO2_RATES[Task::TYPE_TECHNICAL]);

        $expectedEstimated =
            (2.0 * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT]) +
            (5.0 * Task::CO2_RATES[Task::TYPE_TECHNICAL]);

        $this->assertEquals($expectedActual, $comparison['actual']);
        $this->assertEquals($expectedEstimated, $comparison['estimated']);
        $this->assertEquals($expectedActual - $expectedEstimated, $comparison['difference']);

        $expectedPercentage = (($expectedActual - $expectedEstimated) / $expectedEstimated) * 100;
        $this->assertEquals($expectedPercentage, $comparison['percentage_difference']);
    }

    public function testCalculateEnvironmentalImpact(): void
    {
        $co2Kg = 100.0;

        $impact = $this->co2Calculator->calculateEnvironmentalImpact($co2Kg);

        $this->assertArrayHasKey('trees_needed_per_year', $impact);
        $this->assertArrayHasKey('km_car_equivalent', $impact);
        $this->assertArrayHasKey('kwh_renewable_equivalent', $impact);

        $this->assertEquals(100.0 / 22, $impact['trees_needed_per_year']);
        $this->assertEquals(100.0 / 0.12, $impact['km_car_equivalent']);
        $this->assertEquals(100.0 / 0.5, $impact['kwh_renewable_equivalent']);
    }

    public function testCalculateProjectCo2WithMixedTaskTypes(): void
    {
        $project = new Project();

        // Créer plusieurs tâches de chaque type
        $tasks = [
            ['type' => Task::TYPE_OFFICE_LIGHT, 'hours' => '1.0'],
            ['type' => Task::TYPE_OFFICE_LIGHT, 'hours' => '2.0'],
            ['type' => Task::TYPE_TECHNICAL, 'hours' => '3.0'],
            ['type' => Task::TYPE_TECHNICAL, 'hours' => '1.5'],
            ['type' => Task::TYPE_ENERGY_INTENSIVE, 'hours' => '0.5'],
        ];

        $expectedTotal = 0;

        foreach ($tasks as $taskData) {
            $task = new Task();
            $task->setType($taskData['type']);
            $task->setActualHours($taskData['hours']);
            $project->addTask($task);

            $expectedTotal += (float) $taskData['hours'] * Task::CO2_RATES[$taskData['type']];
        }

        $actualCo2 = $this->co2Calculator->calculateProjectCo2($project);
        $this->assertEquals($expectedTotal, $actualCo2);
    }

    public function testCalculateCo2ByTypeWithComplexProject(): void
    {
        $project = new Project();

        // Créer des tâches avec différents types et heures
        $task1 = new Task();
        $task1->setType(Task::TYPE_OFFICE_LIGHT);
        $task1->setActualHours('5.0');
        $project->addTask($task1);

        $task2 = new Task();
        $task2->setType(Task::TYPE_OFFICE_LIGHT);
        $task2->setActualHours('3.0');
        $project->addTask($task2);

        $task3 = new Task();
        $task3->setType(Task::TYPE_TECHNICAL);
        $task3->setActualHours('4.0');
        $project->addTask($task3);

        $co2ByType = $this->co2Calculator->calculateCo2ByType($project);

        $expectedOfficeCo2 = (5.0 + 3.0) * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT];
        $expectedTechnicalCo2 = 4.0 * Task::CO2_RATES[Task::TYPE_TECHNICAL];

        $this->assertEquals($expectedOfficeCo2, $co2ByType[Task::TYPE_OFFICE_LIGHT]);
        $this->assertEquals($expectedTechnicalCo2, $co2ByType[Task::TYPE_TECHNICAL]);
        $this->assertEquals(0.0, $co2ByType[Task::TYPE_ENERGY_INTENSIVE]);
    }

    public function testCompareActualVsEstimatedWithZeroEstimated(): void
    {
        $project = new Project();

        $task = new Task();
        $task->setType(Task::TYPE_OFFICE_LIGHT);
        $task->setActualHours('5.0');
        $task->setEstimatedHours('0');
        $project->addTask($task);

        $comparison = $this->co2Calculator->compareActualVsEstimated($project);

        $expectedActual = 5.0 * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT];

        $this->assertEquals($expectedActual, $comparison['actual']);
        $this->assertEquals(0.0, $comparison['estimated']);
        $this->assertEquals($expectedActual, $comparison['difference']);
        $this->assertEquals(0, $comparison['percentage_difference']);
    }

    public function testCalculateEnvironmentalImpactWithZero(): void
    {
        $impact = $this->co2Calculator->calculateEnvironmentalImpact(0.0);

        $this->assertEquals(0.0, $impact['trees_needed_per_year']);
        $this->assertEquals(0.0, $impact['km_car_equivalent']);
        $this->assertEquals(0.0, $impact['kwh_renewable_equivalent']);
    }

    public function testCalculateEnvironmentalImpactWithSmallValues(): void
    {
        $co2Kg = 1.0;

        $impact = $this->co2Calculator->calculateEnvironmentalImpact($co2Kg);

        $this->assertEquals(1.0 / 22, $impact['trees_needed_per_year']);
        $this->assertEquals(1.0 / 0.12, $impact['km_car_equivalent']);
        $this->assertEquals(1.0 / 0.5, $impact['kwh_renewable_equivalent']);

        // Vérifier que les valeurs sont positives
        $this->assertGreaterThan(0, $impact['trees_needed_per_year']);
        $this->assertGreaterThan(0, $impact['km_car_equivalent']);
        $this->assertGreaterThan(0, $impact['kwh_renewable_equivalent']);
    }
}
