<?php

namespace App\Tests\Service;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use DateTimeInterface;
use PHPUnit\Framework\TestCase;

class ProjectServiceTest extends TestCase
{
    public function testProjectCreation(): void
    {
        $project = new Project();
        $project->setName('Test Project');
        $project->setDescription('Test Description');

        $this->assertEquals('Test Project', $project->getName());
        $this->assertEquals('Test Description', $project->getDescription());
        $this->assertInstanceOf(DateTimeInterface::class, $project->getCreatedAt());
    }

    public function testProjectTaskManagement(): void
    {
        $project = new Project();
        $project->setName('Task Management Project');

        $task1 = new Task();
        $task1->setTitle('Task 1');
        $task1->setStatus(Task::STATUS_TODO);
        $task1->setPriority(Task::PRIORITY_MEDIUM);
        $task1->setType(Task::TYPE_TECHNICAL);

        $task2 = new Task();
        $task2->setTitle('Task 2');
        $task2->setStatus(Task::STATUS_IN_PROGRESS);
        $task2->setPriority(Task::PRIORITY_HIGH);
        $task2->setType(Task::TYPE_OFFICE_LIGHT);

        // Test adding tasks
        $project->addTask($task1);
        $project->addTask($task2);

        $this->assertCount(2, $project->getTasks());
        $this->assertTrue($project->getTasks()->contains($task1));
        $this->assertTrue($project->getTasks()->contains($task2));

        // Test removing task
        $project->removeTask($task1);
        $this->assertCount(1, $project->getTasks());
        $this->assertFalse($project->getTasks()->contains($task1));
        $this->assertTrue($project->getTasks()->contains($task2));
    }

    public function testProjectMemberManagement(): void
    {
        $project = new Project();
        $project->setName('Member Management Project');

        $user1 = new User();
        $user1->setEmail('<EMAIL>');
        $user1->setFirstName('John');
        $user1->setLastName('Doe');

        $user2 = new User();
        $user2->setEmail('<EMAIL>');
        $user2->setFirstName('Jane');
        $user2->setLastName('Smith');

        // Test adding members
        $project->addMember($user1);
        $project->addMember($user2);

        $this->assertCount(2, $project->getMembers());
        $this->assertTrue($project->getMembers()->contains($user1));
        $this->assertTrue($project->getMembers()->contains($user2));

        // Test removing member
        $project->removeMember($user1);
        $this->assertCount(1, $project->getMembers());
        $this->assertFalse($project->getMembers()->contains($user1));
        $this->assertTrue($project->getMembers()->contains($user2));
    }

    public function testProjectCO2EmissionCalculation(): void
    {
        $project = new Project();
        $project->setName('CO2 Test Project');

        // Test empty project
        $this->assertEquals(0.0, $project->getTotalCO2Emissions());

        // Add tasks with different CO2 emissions
        $task1 = new Task();
        $task1->setTitle('Technical Task');
        $task1->setStatus(Task::STATUS_DONE);
        $task1->setPriority(Task::PRIORITY_MEDIUM);
        $task1->setType(Task::TYPE_TECHNICAL);
        $task1->setActualHours(5); // 5 * 1.0 = 5.0 kg CO2

        $task2 = new Task();
        $task2->setTitle('Office Task');
        $task2->setStatus(Task::STATUS_DONE);
        $task2->setPriority(Task::PRIORITY_LOW);
        $task2->setType(Task::TYPE_OFFICE_LIGHT);
        $task2->setActualHours(10); // 10 * 0.1 = 1.0 kg CO2

        $task3 = new Task();
        $task3->setTitle('Energy Intensive Task');
        $task3->setStatus(Task::STATUS_DONE);
        $task3->setPriority(Task::PRIORITY_HIGH);
        $task3->setType(Task::TYPE_ENERGY_INTENSIVE);
        $task3->setActualHours(2); // 2 * 3.5 = 7.0 kg CO2

        $project->addTask($task1);
        $project->addTask($task2);
        $project->addTask($task3);

        // Total: 5.0 + 1.0 + 7.0 = 13.0 kg CO2
        $this->assertEquals(13.0, $project->getTotalCO2Emissions());
    }

    public function testProjectCO2EmissionWithNoHours(): void
    {
        $project = new Project();
        $project->setName('No Hours Project');

        $task = new Task();
        $task->setTitle('Task Without Hours');
        $task->setStatus(Task::STATUS_TODO);
        $task->setPriority(Task::PRIORITY_MEDIUM);
        $task->setType(Task::TYPE_TECHNICAL);
        // No hours set

        $project->addTask($task);

        $this->assertEquals(0.0, $project->getTotalCO2Emissions());
    }

    public function testProjectCO2EmissionWithEstimatedHours(): void
    {
        $project = new Project();
        $project->setName('Estimated Hours Project');

        $task1 = new Task();
        $task1->setTitle('Task with Estimated Hours');
        $task1->setStatus(Task::STATUS_TODO);
        $task1->setPriority(Task::PRIORITY_MEDIUM);
        $task1->setType(Task::TYPE_TECHNICAL);
        $task1->setEstimatedHours(8); // 8 * 1.0 = 8.0 kg CO2

        $task2 = new Task();
        $task2->setTitle('Task with Both Hours');
        $task2->setStatus(Task::STATUS_DONE);
        $task2->setPriority(Task::PRIORITY_MEDIUM);
        $task2->setType(Task::TYPE_OFFICE_LIGHT);
        $task2->setEstimatedHours(5);
        $task2->setActualHours(7); // Uses actual: 7 * 0.1 = 0.7 kg CO2

        $project->addTask($task1);
        $project->addTask($task2);

        // Total: 8.0 + 0.7 = 8.7 kg CO2
        $this->assertEquals(8.7, $project->getTotalCO2Emissions());
    }

    public function testProjectDuplicateTaskHandling(): void
    {
        $project = new Project();
        $project->setName('Duplicate Test Project');

        $task = new Task();
        $task->setTitle('Unique Task');
        $task->setStatus(Task::STATUS_TODO);
        $task->setPriority(Task::PRIORITY_MEDIUM);
        $task->setType(Task::TYPE_TECHNICAL);

        // Add task twice
        $project->addTask($task);
        $project->addTask($task);

        // Should only contain the task once
        $this->assertCount(1, $project->getTasks());
    }

    public function testProjectDuplicateMemberHandling(): void
    {
        $project = new Project();
        $project->setName('Duplicate Member Test Project');

        $user = new User();
        $user->setEmail('<EMAIL>');
        $user->setFirstName('John');
        $user->setLastName('Doe');

        // Add member twice
        $project->addMember($user);
        $project->addMember($user);

        // Should only contain the member once
        $this->assertCount(1, $project->getMembers());
    }

    public function testProjectNameValidation(): void
    {
        $project = new Project();

        // Test empty name
        $project->setName('');
        $this->assertEquals('', $project->getName());

        // Test normal name
        $project->setName('Normal Project Name');
        $this->assertEquals('Normal Project Name', $project->getName());

        // Test long name
        $longName = str_repeat('Very Long Project Name ', 10);
        $project->setName($longName);
        $this->assertEquals($longName, $project->getName());

        // Test special characters
        $specialName = 'Project-Name_With.Special@Characters!';
        $project->setName($specialName);
        $this->assertEquals($specialName, $project->getName());
    }

    public function testProjectDescriptionHandling(): void
    {
        $project = new Project();
        $project->setName('Description Test Project');

        // Test null description
        $this->assertNull($project->getDescription());

        // Test empty description
        $project->setDescription('');
        $this->assertEquals('', $project->getDescription());

        // Test normal description
        $description = 'This is a test project description.';
        $project->setDescription($description);
        $this->assertEquals($description, $project->getDescription());

        // Test multiline description
        $multilineDescription = "Line 1\nLine 2\nLine 3";
        $project->setDescription($multilineDescription);
        $this->assertEquals($multilineDescription, $project->getDescription());

        // Test very long description
        $longDescription = str_repeat('This is a very long description. ', 100);
        $project->setDescription($longDescription);
        $this->assertEquals($longDescription, $project->getDescription());
    }

    public function testProjectDateHandling(): void
    {
        $project = new Project();
        $project->setName('Date Test Project');

        // Test that createdAt is set automatically
        $this->assertInstanceOf(DateTimeInterface::class, $project->getCreatedAt());

        // Test that createdAt is immutable
        $originalCreatedAt = $project->getCreatedAt();
        sleep(1); // Wait a second
        $this->assertEquals($originalCreatedAt, $project->getCreatedAt());
    }

    public function testProjectIdHandling(): void
    {
        $project = new Project();
        $project->setName('ID Test Project');

        // Test that ID is null before persistence
        $this->assertNull($project->getId());
    }

    public function testProjectTaskStatusDistribution(): void
    {
        $project = new Project();
        $project->setName('Status Distribution Project');

        // Create tasks with different statuses
        $todoTask = new Task();
        $todoTask->setTitle('Todo Task');
        $todoTask->setStatus(Task::STATUS_TODO);
        $todoTask->setPriority(Task::PRIORITY_MEDIUM);
        $todoTask->setType(Task::TYPE_TECHNICAL);

        $inProgressTask = new Task();
        $inProgressTask->setTitle('In Progress Task');
        $inProgressTask->setStatus(Task::STATUS_IN_PROGRESS);
        $inProgressTask->setPriority(Task::PRIORITY_HIGH);
        $inProgressTask->setType(Task::TYPE_OFFICE_LIGHT);

        $doneTask = new Task();
        $doneTask->setTitle('Done Task');
        $doneTask->setStatus(Task::STATUS_DONE);
        $doneTask->setPriority(Task::PRIORITY_LOW);
        $doneTask->setType(Task::TYPE_ENERGY_INTENSIVE);

        $project->addTask($todoTask);
        $project->addTask($inProgressTask);
        $project->addTask($doneTask);

        $this->assertCount(3, $project->getTasks());

        // Count tasks by status
        $todoCount = 0;
        $inProgressCount = 0;
        $doneCount = 0;

        foreach ($project->getTasks() as $task) {
            switch ($task->getStatus()) {
                case Task::STATUS_TODO:
                    $todoCount++;
                    break;
                case Task::STATUS_IN_PROGRESS:
                    $inProgressCount++;
                    break;
                case Task::STATUS_DONE:
                    $doneCount++;
                    break;
            }
        }

        $this->assertEquals(1, $todoCount);
        $this->assertEquals(1, $inProgressCount);
        $this->assertEquals(1, $doneCount);
    }
}
