<?php

namespace App\Tests\Service;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use App\Repository\ProjectRepository;
use App\Repository\TaskRepository;
use App\Service\Co2CalculatorService;
use App\Service\StatisticsService;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class StatisticsServiceTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;
    private StatisticsService $statisticsService;
    private TaskRepository $taskRepository;
    private ProjectRepository $projectRepository;

    protected function setUp(): void
    {
        $kernel = self::bootKernel();
        $this->entityManager = $kernel->getContainer()
            ->get('doctrine')
            ->getManager();

        $this->taskRepository = $this->entityManager->getRepository(Task::class);
        $this->projectRepository = $this->entityManager->getRepository(Project::class);

        $co2Calculator = new Co2CalculatorService();
        $this->statisticsService = new StatisticsService(
            $this->taskRepository,
            $this->projectRepository,
            $co2Calculator
        );
    }

    protected function tearDown(): void
    {
        $this->entityManager->createQuery('DELETE FROM App\Entity\Task')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\Project')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\User')->execute();
        parent::tearDown();
    }

    public function testGetDashboardStatistics(): void
    {
        // Créer des tâches avec différents statuts
        $this->createTestTask('Todo Task', Task::STATUS_TODO);
        $this->createTestTask('In Progress Task', Task::STATUS_IN_PROGRESS);
        $this->createTestTask('Done Task 1', Task::STATUS_DONE);
        $this->createTestTask('Done Task 2', Task::STATUS_DONE);

        $stats = $this->statisticsService->getDashboardStatistics();

        $this->assertEquals(4, $stats['total_tasks']);
        $this->assertEquals(1, $stats['todo_tasks']);
        $this->assertEquals(1, $stats['in_progress_tasks']);
        $this->assertEquals(2, $stats['completed_tasks']);
        $this->assertEquals(50.0, $stats['completion_rate']); // 2/4 * 100
    }

    public function testGetDashboardStatisticsWithNoTasks(): void
    {
        $stats = $this->statisticsService->getDashboardStatistics();

        $this->assertEquals(0, $stats['total_tasks']);
        $this->assertEquals(0, $stats['todo_tasks']);
        $this->assertEquals(0, $stats['in_progress_tasks']);
        $this->assertEquals(0, $stats['completed_tasks']);
        $this->assertEquals(0, $stats['completion_rate']);
    }

    public function testGetCo2Statistics(): void
    {
        $this->createTestTaskWithCo2('Office Task', Task::TYPE_OFFICE_LIGHT, '2.0');
        $this->createTestTaskWithCo2('Technical Task', Task::TYPE_TECHNICAL, '3.0');
        $this->createTestTaskWithCo2('Energy Task', Task::TYPE_ENERGY_INTENSIVE, '1.0');

        $stats = $this->statisticsService->getCo2Statistics();

        $expectedTotalCo2 =
            (2.0 * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT]) +
            (3.0 * Task::CO2_RATES[Task::TYPE_TECHNICAL]) +
            (1.0 * Task::CO2_RATES[Task::TYPE_ENERGY_INTENSIVE]);

        $this->assertEquals($expectedTotalCo2, $stats['total_co2']);
        $this->assertArrayHasKey('co2_by_type', $stats);
        $this->assertArrayHasKey('environmental_impact', $stats);

        $this->assertEquals(2.0 * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT], $stats['co2_by_type'][Task::TYPE_OFFICE_LIGHT]);
        $this->assertEquals(3.0 * Task::CO2_RATES[Task::TYPE_TECHNICAL], $stats['co2_by_type'][Task::TYPE_TECHNICAL]);
        $this->assertEquals(1.0 * Task::CO2_RATES[Task::TYPE_ENERGY_INTENSIVE], $stats['co2_by_type'][Task::TYPE_ENERGY_INTENSIVE]);
    }

    public function testGetOverdueTasks(): void
    {
        // Tâche en retard
        $overdueTask = $this->createTestTask('Overdue Task', Task::STATUS_TODO);
        $overdueTask->setDueDate(new DateTimeImmutable('-1 day'));

        // Tâche future
        $futureTask = $this->createTestTask('Future Task', Task::STATUS_TODO);
        $futureTask->setDueDate(new DateTimeImmutable('+1 day'));

        // Tâche terminée en retard (ne devrait pas apparaître)
        $completedOverdueTask = $this->createTestTask('Completed Overdue', Task::STATUS_DONE);
        $completedOverdueTask->setDueDate(new DateTimeImmutable('-1 day'));

        $this->entityManager->flush();

        $overdueTasks = $this->statisticsService->getOverdueTasks();

        $this->assertCount(1, $overdueTasks);
        $this->assertEquals('Overdue Task', $overdueTasks[0]->getTitle());
    }

    public function testGetRecentTasks(): void
    {
        $task1 = $this->createTestTask('Task 1');
        sleep(1);
        $task2 = $this->createTestTask('Task 2');
        sleep(1);
        $task3 = $this->createTestTask('Task 3');

        $recentTasks = $this->statisticsService->getRecentTasks(2);

        $this->assertCount(2, $recentTasks);
        $this->assertEquals('Task 3', $recentTasks[0]->getTitle());
        $this->assertEquals('Task 2', $recentTasks[1]->getTitle());
    }

    public function testGetTaskStatsByPriority(): void
    {
        $this->createTestTask('Low 1', Task::STATUS_TODO, Task::PRIORITY_LOW);
        $this->createTestTask('Low 2', Task::STATUS_TODO, Task::PRIORITY_LOW);
        $this->createTestTask('High 1', Task::STATUS_TODO, Task::PRIORITY_HIGH);
        $this->createTestTask('Urgent 1', Task::STATUS_TODO, Task::PRIORITY_URGENT);

        $stats = $this->statisticsService->getTaskStatsByPriority();

        $this->assertEquals(2, $stats[Task::PRIORITY_LOW]);
        $this->assertEquals(0, $stats[Task::PRIORITY_MEDIUM]);
        $this->assertEquals(1, $stats[Task::PRIORITY_HIGH]);
        $this->assertEquals(1, $stats[Task::PRIORITY_URGENT]);
    }

    public function testGetTaskStatsByType(): void
    {
        $this->createTestTaskWithCo2('Office 1', Task::TYPE_OFFICE_LIGHT);
        $this->createTestTaskWithCo2('Office 2', Task::TYPE_OFFICE_LIGHT);
        $this->createTestTaskWithCo2('Technical 1', Task::TYPE_TECHNICAL);

        $stats = $this->statisticsService->getTaskStatsByType();

        $this->assertEquals(2, $stats[Task::TYPE_OFFICE_LIGHT]);
        $this->assertEquals(1, $stats[Task::TYPE_TECHNICAL]);
        $this->assertEquals(0, $stats[Task::TYPE_ENERGY_INTENSIVE]);
    }

    public function testCalculateProjectCompletionRate(): void
    {
        $project = $this->createTestProject();

        // Créer les tâches directement avec le bon projet
        $task1 = new Task();
        $task1->setTitle('Task 1');
        $task1->setStatus(Task::STATUS_DONE);
        $task1->setProject($project);
        $this->entityManager->persist($task1);

        $task2 = new Task();
        $task2->setTitle('Task 2');
        $task2->setStatus(Task::STATUS_DONE);
        $task2->setProject($project);
        $this->entityManager->persist($task2);

        $task3 = new Task();
        $task3->setTitle('Task 3');
        $task3->setStatus(Task::STATUS_TODO);
        $task3->setProject($project);
        $this->entityManager->persist($task3);

        $task4 = new Task();
        $task4->setTitle('Task 4');
        $task4->setStatus(Task::STATUS_IN_PROGRESS);
        $task4->setProject($project);
        $this->entityManager->persist($task4);

        $this->entityManager->flush();

        // Rafraîchir le projet pour s'assurer que les tâches sont chargées
        $this->entityManager->refresh($project);

        $completionRate = $this->statisticsService->calculateProjectCompletionRate($project);

        $this->assertEquals(50.0, $completionRate); // 2 completed out of 4 total
    }

    public function testCalculateProjectCompletionRateWithNoTasks(): void
    {
        $project = $this->createTestProject();

        $completionRate = $this->statisticsService->calculateProjectCompletionRate($project);

        $this->assertEquals(0.0, $completionRate);
    }

    public function testGetProjectsWithStatistics(): void
    {
        // Nettoyer d'abord tous les projets existants
        $this->entityManager->createQuery('DELETE FROM App\Entity\Task')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\Project')->execute();
        $this->entityManager->createQuery('DELETE FROM App\Entity\User')->execute();

        $project = $this->createTestProject();
        $user = $this->createTestUser();

        $project->addMember($user);

        // Créer les tâches directement avec le bon projet
        $task1 = new Task();
        $task1->setTitle('Task 1');
        $task1->setType(Task::TYPE_TECHNICAL);
        $task1->setActualHours('2.0');
        $task1->setProject($project);
        $task1->setStatus(Task::STATUS_DONE);
        $this->entityManager->persist($task1);

        $task2 = new Task();
        $task2->setTitle('Task 2');
        $task2->setType(Task::TYPE_OFFICE_LIGHT);
        $task2->setActualHours('1.0');
        $task2->setProject($project);
        $task2->setStatus(Task::STATUS_TODO);
        $this->entityManager->persist($task2);

        $this->entityManager->flush();

        // Rafraîchir le projet pour s'assurer que les tâches sont chargées
        $this->entityManager->refresh($project);

        $projectsWithStats = $this->statisticsService->getProjectsWithStatistics();

        $this->assertCount(1, $projectsWithStats);

        $projectStats = $projectsWithStats[0];
        $this->assertEquals($project, $projectStats['project']);
        $this->assertEquals(2, $projectStats['task_count']);
        $this->assertEquals(1, $projectStats['member_count']);
        $this->assertEquals(50.0, $projectStats['completion_rate']);

        $expectedCo2 = (2.0 * Task::CO2_RATES[Task::TYPE_TECHNICAL]) + (1.0 * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT]);
        $this->assertEquals($expectedCo2, $projectStats['co2_emission']);
    }

    private function createTestTask(
        string $title = 'Test Task',
        string $status = Task::STATUS_TODO,
        string $priority = Task::PRIORITY_MEDIUM
    ): Task {
        $project = $this->createTestProject();

        $task = new Task();
        $task->setTitle($title);
        $task->setStatus($status);
        $task->setPriority($priority);
        $task->setProject($project);

        $this->entityManager->persist($task);
        $this->entityManager->flush();

        return $task;
    }

    private function createTestTaskWithCo2(
        string $title = 'Test Task',
        string $type = Task::TYPE_OFFICE_LIGHT,
        string $hours = '1.0'
    ): Task {
        $project = $this->createTestProject();

        $task = new Task();
        $task->setTitle($title);
        $task->setType($type);
        $task->setActualHours($hours);
        $task->setProject($project);

        $this->entityManager->persist($task);
        $this->entityManager->flush();

        return $task;
    }

    private function createTestProject(string $name = 'Test Project'): Project
    {
        $project = new Project();
        $project->setName($name);

        $this->entityManager->persist($project);
        $this->entityManager->flush();

        return $project;
    }

    private function createTestUser(string $email = '<EMAIL>'): User
    {
        $user = new User();
        $user->setEmail($email);
        $user->setFirstName('Test');
        $user->setLastName('User');
        $user->setPassword('password');

        $this->entityManager->persist($user);
        $this->entityManager->flush();

        return $user;
    }
}
