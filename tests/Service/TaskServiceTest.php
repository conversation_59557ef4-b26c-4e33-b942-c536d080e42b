<?php

namespace App\Tests\Service;

use App\Entity\Task;
use DateTimeImmutable;
use DateTimeInterface;
use PHPUnit\Framework\TestCase;

class TaskServiceTest extends TestCase
{
    public function testTaskStatusTransitions(): void
    {
        $task = new Task();
        $task->setTitle('Test Task');
        $task->setStatus(Task::STATUS_TODO);
        $task->setPriority(Task::PRIORITY_MEDIUM);
        $task->setType(Task::TYPE_TECHNICAL);

        // Test transition from TODO to IN_PROGRESS
        $this->assertEquals(Task::STATUS_TODO, $task->getStatus());

        $task->setStatus(Task::STATUS_IN_PROGRESS);
        $this->assertEquals(Task::STATUS_IN_PROGRESS, $task->getStatus());

        // Test transition to DONE
        $task->setStatus(Task::STATUS_DONE);
        $this->assertEquals(Task::STATUS_DONE, $task->getStatus());
    }

    public function testTaskPriorityLevels(): void
    {
        $task = new Task();
        $task->setTitle('Priority Test Task');
        $task->setStatus(Task::STATUS_TODO);
        $task->setType(Task::TYPE_TECHNICAL);

        // Test all priority levels
        $priorities = [
            Task::PRIORITY_LOW,
            Task::PRIORITY_MEDIUM,
            Task::PRIORITY_HIGH,
            Task::PRIORITY_URGENT,
        ];

        foreach ($priorities as $priority) {
            $task->setPriority($priority);
            $this->assertEquals($priority, $task->getPriority());
        }
    }

    public function testTaskTypeCategories(): void
    {
        $task = new Task();
        $task->setTitle('Type Test Task');
        $task->setStatus(Task::STATUS_TODO);
        $task->setPriority(Task::PRIORITY_MEDIUM);

        // Test all task types
        $types = [
            Task::TYPE_OFFICE_LIGHT,
            Task::TYPE_TECHNICAL,
            Task::TYPE_ENERGY_INTENSIVE,
        ];

        foreach ($types as $type) {
            $task->setType($type);
            $this->assertEquals($type, $task->getType());
        }
    }

    public function testTaskCO2EmissionCalculation(): void
    {
        $task = new Task();
        $task->setTitle('CO2 Test Task');
        $task->setStatus(Task::STATUS_DONE);
        $task->setPriority(Task::PRIORITY_MEDIUM);

        // Test office light task
        $task->setType(Task::TYPE_OFFICE_LIGHT);
        $task->setActualHours(10);
        $this->assertEquals(1.0, $task->getCO2Emission()); // 10 * 0.1

        // Test technical task
        $task->setType(Task::TYPE_TECHNICAL);
        $task->setActualHours(5);
        $this->assertEquals(5.0, $task->getCO2Emission()); // 5 * 1.0

        // Test energy intensive task
        $task->setType(Task::TYPE_ENERGY_INTENSIVE);
        $task->setActualHours(2);
        $this->assertEquals(7.0, $task->getCO2Emission()); // 2 * 3.5
    }

    public function testTaskWithEstimatedHours(): void
    {
        $task = new Task();
        $task->setTitle('Estimated Hours Test');
        $task->setStatus(Task::STATUS_TODO);
        $task->setPriority(Task::PRIORITY_MEDIUM);
        $task->setType(Task::TYPE_TECHNICAL);

        // Test with only estimated hours
        $task->setEstimatedHours(8);
        $this->assertEquals(8.0, $task->getCO2Emission()); // Uses estimated hours

        // Test with both estimated and actual hours
        $task->setActualHours(10);
        $this->assertEquals(10.0, $task->getCO2Emission()); // Uses actual hours
    }

    public function testTaskOverdueDetection(): void
    {
        $task = new Task();
        $task->setTitle('Overdue Test Task');
        $task->setStatus(Task::STATUS_TODO);
        $task->setPriority(Task::PRIORITY_MEDIUM);
        $task->setType(Task::TYPE_TECHNICAL);

        // Task without due date should not be overdue
        $this->assertFalse($task->isOverdue());

        // Task with future due date should not be overdue
        $futureDate = new DateTimeImmutable('+1 week');
        $task->setDueDate($futureDate);
        $this->assertFalse($task->isOverdue());

        // Task with past due date should be overdue
        $pastDate = new DateTimeImmutable('-1 week');
        $task->setDueDate($pastDate);
        $this->assertTrue($task->isOverdue());

        // Completed task should not be overdue even if past due date
        $task->setStatus(Task::STATUS_DONE);
        $this->assertFalse($task->isOverdue());
    }

    public function testTaskConstants(): void
    {
        // Test that all constants are defined
        $this->assertIsString(Task::PRIORITY_LOW);
        $this->assertIsString(Task::PRIORITY_MEDIUM);
        $this->assertIsString(Task::PRIORITY_HIGH);
        $this->assertIsString(Task::PRIORITY_URGENT);

        $this->assertIsString(Task::STATUS_TODO);
        $this->assertIsString(Task::STATUS_IN_PROGRESS);
        $this->assertIsString(Task::STATUS_DONE);

        $this->assertIsString(Task::TYPE_OFFICE_LIGHT);
        $this->assertIsString(Task::TYPE_TECHNICAL);
        $this->assertIsString(Task::TYPE_ENERGY_INTENSIVE);

        // Test CO2 rates
        $this->assertIsArray(Task::CO2_RATES);
        $this->assertArrayHasKey(Task::TYPE_OFFICE_LIGHT, Task::CO2_RATES);
        $this->assertArrayHasKey(Task::TYPE_TECHNICAL, Task::CO2_RATES);
        $this->assertArrayHasKey(Task::TYPE_ENERGY_INTENSIVE, Task::CO2_RATES);
    }

    public function testTaskStaticMethods(): void
    {
        // Test getPriorities method
        $priorities = Task::getPriorities();
        $this->assertIsArray($priorities);
        $this->assertArrayHasKey(Task::PRIORITY_LOW, $priorities);
        $this->assertArrayHasKey(Task::PRIORITY_MEDIUM, $priorities);
        $this->assertArrayHasKey(Task::PRIORITY_HIGH, $priorities);
        $this->assertArrayHasKey(Task::PRIORITY_URGENT, $priorities);

        // Test getStatuses method
        $statuses = Task::getStatuses();
        $this->assertIsArray($statuses);
        $this->assertArrayHasKey(Task::STATUS_TODO, $statuses);
        $this->assertArrayHasKey(Task::STATUS_IN_PROGRESS, $statuses);
        $this->assertArrayHasKey(Task::STATUS_DONE, $statuses);

        // Test getTypes method
        $types = Task::getTypes();
        $this->assertIsArray($types);
        $this->assertArrayHasKey(Task::TYPE_OFFICE_LIGHT, $types);
        $this->assertArrayHasKey(Task::TYPE_TECHNICAL, $types);
        $this->assertArrayHasKey(Task::TYPE_ENERGY_INTENSIVE, $types);
    }

    public function testTaskCO2RateRetrieval(): void
    {
        $task = new Task();
        $task->setTitle('CO2 Rate Test');
        $task->setStatus(Task::STATUS_TODO);
        $task->setPriority(Task::PRIORITY_MEDIUM);

        // Test CO2 rate for office light
        $task->setType(Task::TYPE_OFFICE_LIGHT);
        $this->assertEquals(0.1, $task->getCO2Rate());

        // Test CO2 rate for technical
        $task->setType(Task::TYPE_TECHNICAL);
        $this->assertEquals(1.0, $task->getCO2Rate());

        // Test CO2 rate for energy intensive
        $task->setType(Task::TYPE_ENERGY_INTENSIVE);
        $this->assertEquals(3.5, $task->getCO2Rate());
    }

    public function testTaskWithZeroHours(): void
    {
        $task = new Task();
        $task->setTitle('Zero Hours Test');
        $task->setStatus(Task::STATUS_TODO);
        $task->setPriority(Task::PRIORITY_MEDIUM);
        $task->setType(Task::TYPE_TECHNICAL);

        // Task with no hours should have zero CO2 emission
        $this->assertEquals(0.0, $task->getCO2Emission());

        // Task with zero actual hours
        $task->setActualHours(0);
        $this->assertEquals(0.0, $task->getCO2Emission());

        // Task with zero estimated hours
        $task->setActualHours(null);
        $task->setEstimatedHours(0);
        $this->assertEquals(0.0, $task->getCO2Emission());
    }

    public function testTaskDateHandling(): void
    {
        $task = new Task();
        $task->setTitle('Date Test Task');
        $task->setStatus(Task::STATUS_TODO);
        $task->setPriority(Task::PRIORITY_MEDIUM);
        $task->setType(Task::TYPE_TECHNICAL);

        // Test that createdAt is set automatically
        $this->assertInstanceOf(DateTimeInterface::class, $task->getCreatedAt());

        // Test due date setting
        $dueDate = new DateTimeImmutable('+1 month');
        $task->setDueDate($dueDate);
        $this->assertEquals($dueDate, $task->getDueDate());
    }

    public function testTaskDescriptionHandling(): void
    {
        $task = new Task();
        $task->setTitle('Description Test');
        $task->setStatus(Task::STATUS_TODO);
        $task->setPriority(Task::PRIORITY_MEDIUM);
        $task->setType(Task::TYPE_TECHNICAL);

        // Test empty description
        $this->assertNull($task->getDescription());

        // Test setting description
        $description = 'This is a test task description with multiple lines.\nSecond line here.';
        $task->setDescription($description);
        $this->assertEquals($description, $task->getDescription());

        // Test long description
        $longDescription = str_repeat('This is a very long description. ', 100);
        $task->setDescription($longDescription);
        $this->assertEquals($longDescription, $task->getDescription());
    }
}
